import { mount } from '@vue/test-utils'
import { describe, expect, test, vi } from 'vitest'
import { provide, ref } from 'vue'
import NeRenderCore from '../render-core.vue'

describe('NeRenderCore', () => {
  test('render simple div element', () => {
    const config = {
      id: 'test-div',
      type: 'div',
      props: {
        class: 'test-class'
      },
      slots: {
        default: 'Hello World'
      }
    }

    const wrapper = mount(NeRenderCore, {
      props: { config }
    })

    expect(wrapper.find('div.test-class').exists()).toBe(true)
    expect(wrapper.text()).toContain('Hello World')
  })

  test('render nested elements', () => {
    const config = {
      id: 'container',
      type: 'div',
      props: {
        class: 'container'
      },
      elements: [
        {
          id: 'title',
          type: 'h1',
          slots: {
            default: 'Title'
          }
        },
        {
          id: 'content',
          type: 'p',
          slots: {
            default: 'Content'
          }
        }
      ]
    }

    const wrapper = mount(NeRenderCore, {
      props: { config }
    })

    expect(wrapper.find('div.container').exists()).toBe(true)
    expect(wrapper.find('h1').exists()).toBe(true)
    expect(wrapper.find('p').exists()).toBe(true)
    expect(wrapper.text()).toContain('Title')
    expect(wrapper.text()).toContain('Content')
  })

  test('handle click events', async () => {
    const clickHandler = vi.fn()
    
    const config = {
      id: 'test-button',
      type: 'button',
      events: [
        {
          name: 'click',
          handler: clickHandler
        }
      ],
      slots: {
        default: 'Click me'
      }
    }

    const wrapper = mount(NeRenderCore, {
      props: { config }
    })

    await wrapper.find('button').trigger('click')
    expect(clickHandler).toHaveBeenCalled()
  })

  test('handle string event handlers with refs', async () => {
    const mockHandler = vi.fn()
    const refs = { testHandler: mockHandler }
    
    const config = {
      id: 'test-button',
      type: 'button',
      events: [
        {
          name: 'click',
          handler: 'testHandler'
        }
      ],
      slots: {
        default: 'Click me'
      }
    }

    const wrapper = mount({
      template: '<NeRenderCore :config="config" />',
      components: { NeRenderCore },
      setup() {
        provide('refs', refs)
        return { config }
      }
    })

    await wrapper.find('button').trigger('click')
    expect(mockHandler).toHaveBeenCalled()
  })

  test('set component ref correctly', () => {
    const refs = {}
    
    const config = {
      id: 'test-input',
      type: 'input',
      props: {
        value: 'test value'
      }
    }

    mount({
      template: '<NeRenderCore :config="config" />',
      components: { NeRenderCore },
      setup() {
        provide('refs', refs)
        return { config }
      }
    })

    expect(refs['test-input']).toBeDefined()
  })

  test('render complex slot content', () => {
    const config = {
      id: 'card',
      type: 'div',
      props: {
        class: 'card'
      },
      slots: {
        header: 'Card Header',
        default: [
          {
            id: 'btn1',
            type: 'button',
            slots: { default: 'Button 1' }
          },
          {
            id: 'btn2',
            type: 'button',
            slots: { default: 'Button 2' }
          }
        ]
      }
    }

    const wrapper = mount(NeRenderCore, {
      props: { config }
    })

    expect(wrapper.find('div.card').exists()).toBe(true)
    expect(wrapper.findAll('button')).toHaveLength(2)
    expect(wrapper.text()).toContain('Button 1')
    expect(wrapper.text()).toContain('Button 2')
  })

  test('handle missing component gracefully', () => {
    const config = {
      id: 'unknown',
      type: 'unknown-component',
      slots: {
        default: 'Unknown component'
      }
    }

    const wrapper = mount(NeRenderCore, {
      props: { config }
    })

    // Should render as the component type name (fallback)
    expect(wrapper.html()).toContain('unknown-component')
  })
})
