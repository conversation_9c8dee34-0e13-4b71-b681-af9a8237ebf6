import { buildProp, buildProps } from '@neue-plus/utils'
import type { Component, ExtractPropTypes, VNode } from 'vue'

// 材料类型
export type NeMaterialType =
  | 'text'
  | 'image'
  | 'video'
  | 'audio'
  | 'iframe'
  | 'component'
  | 'html'
  | 'markdown'

// 材料数据接口
export interface NeMaterialData {
  id?: string | number
  type: NeMaterialType
  content?: string
  src?: string
  alt?: string
  title?: string
  width?: string | number
  height?: string | number
  style?: Record<string, any>
  className?: string
  props?: Record<string, any>
  component?: Component | string
  children?: NeMaterialData[]
  [key: string]: any
}

// 渲染选项
export interface NeRenderOptions {
  // 是否启用安全模式（过滤危险内容）
  safe?: boolean
  // 自定义组件映射
  components?: Record<string, Component>
  // 自定义渲染器
  renderers?: Record<string, (data: NeMaterialData) => VNode | string>
  // 是否启用懒加载
  lazy?: boolean
  // 错误处理
  onError?: (error: Error, data: NeMaterialData) => void
}

export const neMaterialRenderProps = buildProps({
  // 材料数据
  data: buildProp({
    type: [Object, Array] as () => NeMaterialData | NeMaterialData[],
    required: true,
  }),

  // 渲染选项
  options: buildProp({
    type: Object as () => NeRenderOptions,
    default: () => ({}),
  }),

  // 是否启用安全模式
  safe: buildProp({
    type: Boolean,
    default: true,
  }),

  // 是否启用懒加载
  lazy: buildProp({
    type: Boolean,
    default: false,
  }),

  // 自定义标签名
  tag: buildProp({
    type: String,
    default: 'div',
  }),

  // 是否启用动画
  animated: buildProp({
    type: Boolean,
    default: false,
  }),

  // 动画持续时间
  animationDuration: buildProp({
    type: Number,
    default: 300,
  }),
} as const)

export type NeMaterialRenderProps = ExtractPropTypes<
  typeof neMaterialRenderProps
>

// 事件类型
export type NeMaterialRenderEmits = {
  (e: 'render', data: NeMaterialData): void
  (e: 'error', error: Error, data: NeMaterialData): void
  (e: 'load', data: NeMaterialData): void
  (e: 'click', event: Event, data: NeMaterialData): void
}
