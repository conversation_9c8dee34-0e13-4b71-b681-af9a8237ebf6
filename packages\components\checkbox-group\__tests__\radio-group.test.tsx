import { mount } from '@vue/test-utils'
import { describe, expect, test } from 'vitest'
import { ref } from 'vue'
import RadioGroup from '../src/checkbox-group.vue'

const mockOptions = [
  { label: '选项1', value: 'option1' },
  { label: '选项2', value: 'option2' },
  { label: '选项3', value: 'option3', disabled: true },
]

describe('NeRadioGroup', () => {
  test('render with options', () => {
    const wrapper = mount(RadioGroup, {
      props: {
        modelValue: 'option1',
        options: mockOptions,
      },
    })

    expect(wrapper.find('.ne-radio-group').exists()).toBe(true)
    expect(wrapper.findAll('.el-radio')).toHaveLength(3)
    expect(wrapper.find('.el-radio input[value="option1"]').element.checked).toBe(true)
  })

  test('emit update:modelValue when value changes', async () => {
    const wrapper = mount(RadioGroup, {
      props: {
        modelValue: 'option1',
        options: mockOptions,
      },
    })

    await wrapper.find('.el-radio input[value="option2"]').trigger('change')
    expect(wrapper.emitted('update:modelValue')).toBeTruthy()
    expect(wrapper.emitted('update:modelValue')?.[0]).toEqual(['option2'])
  })

  test('emit change event when value changes', async () => {
    const wrapper = mount(RadioGroup, {
      props: {
        modelValue: 'option1',
        options: mockOptions,
      },
    })

    await wrapper.find('.el-radio input[value="option2"]').trigger('change')
    expect(wrapper.emitted('change')).toBeTruthy()
    expect(wrapper.emitted('change')?.[0]).toEqual(['option2'])
  })

  test('disabled option should not be selectable', () => {
    const wrapper = mount(RadioGroup, {
      props: {
        modelValue: 'option1',
        options: mockOptions,
      },
    })

    const disabledRadio = wrapper.find('.el-radio input[value="option3"]')
    expect(disabledRadio.element.disabled).toBe(true)
  })

  test('vertical direction', () => {
    const wrapper = mount(RadioGroup, {
      props: {
        modelValue: 'option1',
        options: mockOptions,
        direction: 'vertical',
      },
    })

    expect(wrapper.find('.ne-radio-group--vertical').exists()).toBe(true)
  })

  test('bordered style', () => {
    const wrapper = mount(RadioGroup, {
      props: {
        modelValue: 'option1',
        options: mockOptions,
        bordered: true,
      },
    })

    expect(wrapper.find('.ne-radio-group--bordered').exists()).toBe(true)
  })

  test('custom gap', () => {
    const wrapper = mount(RadioGroup, {
      props: {
        modelValue: 'option1',
        options: mockOptions,
        gap: '20px',
      },
    })

    const style = wrapper.find('.ne-radio-group').element.style
    expect(style.gap).toBe('20px')
  })

  test('slot content', () => {
    const wrapper = mount(RadioGroup, {
      props: {
        modelValue: 'custom',
      },
      slots: {
        default: '<el-radio value="custom">自定义选项</el-radio>',
      },
    })

    expect(wrapper.text()).toContain('自定义选项')
  })
})
