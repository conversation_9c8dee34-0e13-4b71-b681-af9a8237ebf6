declare module 'vue' {
  // GlobalComponents for Volar
  export interface GlobalComponents {
    NeCard: typeof import('neue-plus')['NeCard']
    NeTable: typeof import('neue-plus')['NeTable']
    NeProTable: typeof import('neue-plus')['NeProTable']
    NeDynamicForm: typeof import('neue-plus')['NeDynamicForm']
    NeRadioGroup: typeof import('neue-plus')['NeRadioGroup']
    NeConfigProvider: typeof import('neue-plus')['NeConfigProvider']
    NeMaterialRender: typeof import('neue-plus')['NeMaterialRender']
    NeRenderCore: typeof import('neue-plus')['NeRenderCore']
  }

  interface ComponentCustomProperties {
    $message: typeof import('neue-plus')['ElMessage']
  }
}

export {}
