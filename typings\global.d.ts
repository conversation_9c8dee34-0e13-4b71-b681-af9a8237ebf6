declare module 'vue' {
  // GlobalComponents for Volar
  export interface GlobalComponents {
    NeCard: typeof import('neue-plus')['NeCard']
    NeTable: typeof import('neue-plus')['NeTable']
    NeProTable: typeof import('neue-plus')['NeProTable']
    NeDynamicForm: typeof import('neue-plus')['NeDynamicForm']
    NeRadioGroup: typeof import('neue-plus')['NeRadioGroup']
  }

  interface ComponentCustomProperties {
    $message: typeof import('neue-plus')['ElMessage']
  }
}

export {}
