# NeRenderCore 使用指南

NeRenderCore 是一个基于配置的动态渲染引擎，支持通过 JSON 配置来动态生成 Vue 组件。

## 基本概念

### NeMaterialElementProps 接口

```typescript
interface NeMaterialElementProps {
  id?: string                           // 元素唯一标识
  type: string                         // 组件类型，如 'div', 'el-button' 等
  name?: string                        // 组件名称
  props?: Record<string, any>          // 组件属性
  slots?: Record<string, any>          // 插槽内容
  elements?: NeMaterialElementProps[]  // 子元素
  events?: ElementEvent[]              // 事件配置
  config?: any                         // 额外配置
}
```

### ElementEvent 接口

```typescript
interface ElementEvent {
  name: string                         // 事件名称，如 'click', 'input'
  handler: string | ((...args: any[]) => any)  // 事件处理器
}
```

## 基础用法

### 1. 简单元素渲染

```vue
<template>
  <NeRenderCore :config="simpleConfig" />
</template>

<script setup>
import { ref } from 'vue'
import NeRenderCore from '@/components/material-render/src/render-core/render-core.vue'

const simpleConfig = ref({
  id: 'simple-div',
  type: 'div',
  props: {
    class: 'demo-container',
    style: {
      padding: '20px',
      border: '1px solid #ddd',
      borderRadius: '8px'
    }
  },
  slots: {
    default: 'Hello NeRenderCore!'
  }
})
</script>
```

### 2. 嵌套元素渲染

```vue
<template>
  <NeRenderCore :config="nestedConfig" />
</template>

<script setup>
const nestedConfig = ref({
  id: 'container',
  type: 'div',
  props: {
    class: 'container'
  },
  elements: [
    {
      id: 'title',
      type: 'h2',
      slots: {
        default: '标题'
      }
    },
    {
      id: 'content',
      type: 'p',
      slots: {
        default: '这是内容段落'
      }
    }
  ]
})
</script>
```

### 3. Element Plus 组件渲染

```vue
<template>
  <NeRenderCore :config="buttonConfig" />
</template>

<script setup>
const buttonConfig = ref({
  id: 'my-button',
  type: 'el-button',
  props: {
    type: 'primary',
    size: 'large',
    icon: 'Plus'
  },
  events: [
    {
      name: 'click',
      handler: () => {
        console.log('按钮被点击了！')
      }
    }
  ],
  slots: {
    default: '点击我'
  }
})
</script>
```

### 4. 表单组件渲染

```vue
<template>
  <NeRenderCore :config="formConfig" />
</template>

<script setup>
const formConfig = ref({
  id: 'demo-form',
  type: 'el-form',
  props: {
    model: {},
    'label-width': '120px'
  },
  elements: [
    {
      id: 'name-item',
      type: 'el-form-item',
      props: {
        label: '姓名',
        prop: 'name'
      },
      elements: [
        {
          id: 'name-input',
          type: 'el-input',
          props: {
            placeholder: '请输入姓名'
          }
        }
      ]
    },
    {
      id: 'submit-item',
      type: 'el-form-item',
      elements: [
        {
          id: 'submit-btn',
          type: 'el-button',
          props: {
            type: 'primary'
          },
          events: [
            {
              name: 'click',
              handler: () => {
                console.log('提交表单')
              }
            }
          ],
          slots: {
            default: '提交'
          }
        }
      ]
    }
  ]
})
</script>
```

## 高级用法

### 1. 事件处理

```vue
<template>
  <div>
    <!-- 提供 refs 上下文 -->
    <NeRenderCore :config="config" />
  </div>
</template>

<script setup>
import { provide, ref } from 'vue'

// 创建 refs 对象
const refs = ref({})
provide('refs', refs.value)

// 定义事件处理方法
refs.value.handleButtonClick = (event) => {
  console.log('按钮点击事件:', event)
}

const config = ref({
  id: 'event-button',
  type: 'el-button',
  events: [
    {
      name: 'click',
      handler: 'handleButtonClick'  // 使用字符串引用 refs 中的方法
    }
  ],
  slots: {
    default: '事件按钮'
  }
})
</script>
```

### 2. 动态插槽内容

```vue
<template>
  <NeRenderCore :config="slotConfig" />
</template>

<script setup>
const slotConfig = ref({
  id: 'card-with-slots',
  type: 'el-card',
  props: {
    header: '卡片标题'
  },
  slots: {
    // 简单文本插槽
    default: '这是卡片内容',
    
    // 复杂插槽（数组形式）
    footer: [
      {
        id: 'footer-btn1',
        type: 'el-button',
        props: { size: 'small' },
        slots: { default: '取消' }
      },
      {
        id: 'footer-btn2',
        type: 'el-button',
        props: { type: 'primary', size: 'small' },
        slots: { default: '确定' }
      }
    ]
  }
})
</script>
```

### 3. 组件引用管理

```vue
<template>
  <div>
    <NeRenderCore :config="config" />
    <el-button @click="getInputValue">获取输入框值</el-button>
  </div>
</template>

<script setup>
import { provide, ref } from 'vue'

const refs = ref({})
provide('refs', refs.value)

const config = ref({
  id: 'my-input',
  type: 'el-input',
  props: {
    placeholder: '请输入内容'
  }
})

// 获取组件引用
const getInputValue = () => {
  const inputRef = refs.value['my-input']
  if (inputRef) {
    console.log('输入框的值:', inputRef.value)
  }
}
</script>
```

## 最佳实践

### 1. 组件类型映射

确保在 `utils/index.ts` 中正确映射了所有需要使用的组件：

```typescript
const componentMap = {
  'el-button': () => import('element-plus').then(m => m.ElButton),
  'el-input': () => import('element-plus').then(m => m.ElInput),
  // ... 更多组件
}
```

### 2. 事件处理最佳实践

- 优先使用函数形式的事件处理器
- 使用字符串引用时，确保在 refs 中正确定义了对应的方法
- 避免在配置中直接使用复杂的业务逻辑

### 3. 性能优化

- 使用 `defineAsyncComponent` 避免循环引用
- 合理使用组件的 `key` 属性
- 避免在配置中使用过深的嵌套结构

### 4. 调试技巧

- 使用浏览器开发者工具查看生成的 DOM 结构
- 在事件处理器中添加 console.log 来调试事件流
- 检查 refs 对象确保组件引用正确设置

## 注意事项

1. **循环引用**: 使用 `defineAsyncComponent` 来避免组件间的循环引用
2. **类型安全**: 确保配置对象符合 `NeMaterialElementProps` 接口
3. **组件注册**: 使用的组件必须在 `componentMap` 中正确注册
4. **事件命名**: 事件名称使用小写，如 'click', 'input', 'change'
5. **插槽内容**: 简单文本使用字符串，复杂内容使用数组形式
