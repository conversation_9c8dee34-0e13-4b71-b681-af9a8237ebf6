@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(material-render) {
  width: 100%;

  @include m(animated) {
    .#{$namespace}-material-render__item {
      transition: all var(--ne-animation-duration, 300ms) ease;
    }
  }

  @include m(lazy) {
    // 懒加载样式
  }

  @include e(item) {
    margin-bottom: getCssVar('material-render', 'item-margin-bottom');

    &:last-child {
      margin-bottom: 0;
    }

    @include m(animated) {
      transition: all var(--ne-animation-duration, 300ms) ease;
    }
  }

  @include e(warning) {
    padding: getCssVar('material-render', 'message-padding');
    background-color: getCssVar('color', 'warning', 'light-9');
    border: 1px solid getCssVar('color', 'warning', 'light-7');
    border-radius: getCssVar('border-radius', 'base');
    color: getCssVar('color', 'warning', 'dark-2');
    font-size: getCssVar('font-size', 'base');
  }

  @include e(error) {
    padding: getCssVar('material-render', 'message-padding');
    background-color: getCssVar('color', 'danger', 'light-9');
    border: 1px solid getCssVar('color', 'danger', 'light-5');
    border-radius: getCssVar('border-radius', 'base');
    color: getCssVar('color', 'danger', 'dark-2');
    font-size: getCssVar('font-size', 'base');
    cursor: help;

    &:hover {
      background-color: getCssVar('color', 'danger', 'light-8');
    }
  }

  @include e(markdown) {
    line-height: getCssVar('material-render', 'markdown-line-height');

    h1, h2, h3, h4, h5, h6 {
      margin-top: 0;
      margin-bottom: getCssVar('material-render', 'heading-margin-bottom');
      font-weight: 600;
    }

    p {
      margin-bottom: getCssVar('material-render', 'paragraph-margin-bottom');
    }

    code {
      padding: getCssVar('material-render', 'code-padding');
      background-color: getCssVar('fill-color', 'light');
      border-radius: getCssVar('border-radius', 'small');
      font-size: 85%;
      font-family: getCssVar('font-family', 'mono');
    }

    pre {
      padding: getCssVar('material-render', 'pre-padding');
      background-color: getCssVar('fill-color', 'light');
      border-radius: getCssVar('border-radius', 'base');
      overflow: auto;
      font-family: getCssVar('font-family', 'mono');

      code {
        padding: 0;
        background-color: transparent;
        border-radius: 0;
      }
    }

    blockquote {
      margin: getCssVar('material-render', 'blockquote-margin');
      padding: getCssVar('material-render', 'blockquote-padding');
      border-left: 4px solid getCssVar('color', 'primary');
      background-color: getCssVar('fill-color', 'lighter');
      color: getCssVar('text-color', 'secondary');
    }

    ul, ol {
      padding-left: getCssVar('material-render', 'list-padding-left');
    }

    li {
      margin-bottom: getCssVar('material-render', 'list-item-margin-bottom');
    }

    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: getCssVar('material-render', 'table-margin-bottom');
    }

    th, td {
      padding: getCssVar('material-render', 'table-cell-padding');
      border: 1px solid getCssVar('border-color');
      text-align: left;
    }

    th {
      background-color: getCssVar('fill-color', 'light');
      font-weight: 600;
    }
  }
}
