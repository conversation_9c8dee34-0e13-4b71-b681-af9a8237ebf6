import type { BaseProps, Slot } from './base'
import type { ElementEvent } from './event'

export interface SchemaElement {
  id: string
  type: string
  name?: string
  props?: BaseProps
  slots?: Slot
  elements?: SchemaElement[]
  events?: ElementEvent[]
}

// NeMaterialElement 属性接口
export interface NeMaterialElementProps {
  id?: string
  type: string
  name?: string
  props?: BaseProps
  slots?: Record<string, any>
  elements?: NeMaterialElementProps[]
  events?: ElementEvent[]
  config?: any
}
