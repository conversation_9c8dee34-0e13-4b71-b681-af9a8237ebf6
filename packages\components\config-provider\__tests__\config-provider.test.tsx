import { mount } from '@vue/test-utils'
import { describe, expect, test } from 'vitest'
import { ref } from 'vue'
import ConfigProvider from '../src/config-provider.vue'

describe('NeConfigProvider', () => {
  test('render with default props', () => {
    const wrapper = mount(ConfigProvider, {
      slots: {
        default: '<div>test content</div>',
      },
    })

    expect(wrapper.find('.ne-config-provider').exists()).toBe(true)
    expect(wrapper.text()).toContain('test content')
  })

  test('apply theme config', () => {
    const theme = {
      primaryColor: '#ff0000',
      successColor: '#00ff00',
      borderRadius: '8px',
    }

    const wrapper = mount(ConfigProvider, {
      props: {
        theme,
      },
      slots: {
        default: '<div>test content</div>',
      },
    })

    const style = wrapper.find('.ne-config-provider').element.style
    expect(style.getPropertyValue('--ne-color-primary')).toBe('#ff0000')
    expect(style.getPropertyValue('--ne-color-success')).toBe('#00ff00')
    expect(style.getPropertyValue('--ne-border-radius')).toBe('8px')
  })

  test('dark mode', () => {
    const wrapper = mount(ConfigProvider, {
      props: {
        dark: true,
      },
      slots: {
        default: '<div>test content</div>',
      },
    })

    expect(wrapper.find('.ne-config-provider--dark').exists()).toBe(true)
    expect(wrapper.attributes('data-theme')).toBe('dark')
  })

  test('custom size', () => {
    const wrapper = mount(ConfigProvider, {
      props: {
        size: 'large',
      },
      slots: {
        default: '<div>test content</div>',
      },
    })

    // 这里可以测试 provide 的值，但需要子组件来验证
    expect(wrapper.exists()).toBe(true)
  })

  test('custom namespace', () => {
    const wrapper = mount(ConfigProvider, {
      props: {
        namespace: 'custom',
      },
      slots: {
        default: '<div>test content</div>',
      },
    })

    expect(wrapper.exists()).toBe(true)
  })

  test('strict mode', () => {
    const wrapper = mount(ConfigProvider, {
      props: {
        strict: true,
      },
      slots: {
        default: '<div>test content</div>',
      },
    })

    expect(wrapper.find('.ne-config-provider--strict').exists()).toBe(true)
  })
})
