import { computed } from 'vue'
import type { NeMaterialElementProps } from '../../types'

/**
 * 获取组件事件处理器
 * @param config 元素配置
 * @param refs 引用对象
 * @returns 事件处理器对象
 */
export function getComponentEvent(config: NeMaterialElementProps, refs?: any) {
  const eventHandlers: Record<string, any> = {}
  
  if (config.events && Array.isArray(config.events)) {
    config.events.forEach(event => {
      if (event.name && event.handler) {
        // 将事件名转换为 Vue 的事件格式 (onClick, onInput 等)
        const eventName = `on${event.name.charAt(0).toUpperCase()}${event.name.slice(1)}`
        
        if (typeof event.handler === 'function') {
          eventHandlers[eventName] = event.handler
        } else if (typeof event.handler === 'string') {
          // 如果是字符串，尝试从 refs 中获取方法
          eventHandlers[eventName] = (...args: any[]) => {
            if (refs && refs[event.handler]) {
              return refs[event.handler](...args)
            } else {
              console.warn(`Event handler "${event.handler}" not found in refs`)
            }
          }
        }
      }
    })
  }
  
  return eventHandlers
}

/**
 * 处理元素属性
 * @param config 元素配置
 * @returns 处理后的属性对象
 */
export function useElementProps(config: NeMaterialElementProps) {
  const elProps = computed(() => {
    const props: Record<string, any> = {}
    
    // 复制基础属性
    if (config.props) {
      Object.assign(props, config.props)
    }
    
    // 处理特殊属性
    if (config.name) {
      props.name = config.name
    }
    
    // 处理样式类
    if (config.props?.class) {
      props.class = config.props.class
    }
    
    // 处理内联样式
    if (config.props?.style) {
      props.style = config.props.style
    }
    
    return props
  })
  
  return {
    elProps: elProps.value
  }
}

/**
 * Schema 引用管理 Hook
 */
export interface SchemaRefs {
  [key: string]: any
}

/**
 * 创建 Schema 引用管理器
 * @returns 引用管理器
 */
export function useSchemaRefs() {
  const refs: SchemaRefs = {}
  
  /**
   * 设置引用
   * @param id 元素ID
   * @param ref 引用对象
   */
  const setRef = (id: string, ref: any) => {
    refs[id] = ref
  }
  
  /**
   * 获取引用
   * @param id 元素ID
   * @returns 引用对象
   */
  const getRef = (id: string) => {
    return refs[id]
  }
  
  /**
   * 移除引用
   * @param id 元素ID
   */
  const removeRef = (id: string) => {
    delete refs[id]
  }
  
  /**
   * 清空所有引用
   */
  const clearRefs = () => {
    Object.keys(refs).forEach(key => {
      delete refs[key]
    })
  }
  
  /**
   * 获取所有引用
   * @returns 所有引用对象
   */
  const getAllRefs = () => {
    return { ...refs }
  }
  
  return {
    refs,
    setRef,
    getRef,
    removeRef,
    clearRefs,
    getAllRefs
  }
}
