import { defineComponent, h, provide } from "vue";
import type { NeMaterialRenderProps } from "./types";
import NeConfigProvider from "../NeConfigProvider";
import NeRenderCore from "../NeRenderCore";
import { useSchemaRefs } from "../../hooks/useSchemaRefs";

const NeMaterialRender = defineComponent({
  name: "NeMaterialRender",
  props: {
    config: {
      type: Object as () => NeMaterialRenderProps,
      required: true,
    },
  },
  setup(props, { emit, expose,}) {
    let { config, events, apis, elements } = props.config;
    const refs = useSchemaRefs(elements);
    provide('refs',refs)
    return { config, elements };
  },
  render() {
    return (
      <NeConfigProvider config={this.config.configProvider}>
        {this.elements?.map((child, index) => h(NeRenderCore, { key: child.id, config: child }))}
      </NeConfigProvider>
    );
  },
});

export default NeMaterialRender;
