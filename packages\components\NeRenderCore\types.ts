import { ComponentType } from "../../constant/type";
export interface EventType<T = any> {
  nickName: string; // 中文名称
  eventName: string; // 英文标识
  actions: T[]; // 事件行为
}
export interface NeMaterialElementProps {
    id: string;
    name: string; // 组件名称，
    type: ComponentType;
    props?: Record<string, any>; // 组件的 props
    slots?: Record<string, string>; // 具名插槽内容
    elements?: NeMaterialElementProps[]; // 子组件
    events: EventType[]; //事件配置   
  }