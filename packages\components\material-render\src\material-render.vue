<template>
  <component :is="tag" :class="rootClass" :style="rootStyle">
    <template v-if="Array.isArray(data)">
      <div
        v-for="(item, index) in data"
        :key="item.id || index"
        :class="itemClass"
        @click="handleClick($event, item)"
      >
        <MaterialItem
          :data="item"
          :options="mergedOptions"
          @render="handleRender"
          @error="handleError"
          @load="handleLoad"
        />
      </div>
    </template>
    <template v-else>
      <MaterialItem
        :data="data"
        :options="mergedOptions"
        @render="handleRender"
        @error="handleError"
        @load="handleLoad"
        @click="handleClick"
      />
    </template>
  </component>
</template>

<script lang="ts" setup>
import { computed, defineComponent, h, resolveComponent } from 'vue'

defineOptions({
  name: 'NeMaterialRender',
})

const props = defineProps(neMaterialRenderProps)
const emit = defineEmits(['render', 'error', 'load', 'click'])

// 合并渲染选项
const mergedOptions = computed<NeRenderOptions>(() => ({
  safe: props.safe,
  lazy: props.lazy,
  ...props.options,
}))

// 根元素类名
const rootClass = computed(() => [
  'ne-material-render',
  {
    'ne-material-render--animated': props.animated,
    'ne-material-render--lazy': props.lazy,
  },
])

// 根元素样式
const rootStyle = computed(() => ({
  '--ne-animation-duration': `${props.animationDuration}ms`,
}))

// 项目类名
const itemClass = computed(() => [
  'ne-material-render__item',
  {
    'ne-material-render__item--animated': props.animated,
  },
])

// 材料项组件
const MaterialItem = defineComponent({
  name: 'MaterialItem',
  props: {
    data: {
      type: Object as () => NeMaterialData,
      required: true,
    },
  },
  setup(props, { emit }) {
    const renderMaterial = (data: NeMaterialData) => {
      try {
        emit('render', data)

        // 自定义渲染器
        if (props.options.renderers?.[data.type]) {
          return props.options.renderers[data.type](data)
        }

        // 根据类型渲染
        switch (data.type) {
          case 'text':
            return h(
              'span',
              {
                class: data.className,
                style: data.style,
              },
              data.content
            )

          case 'image':
            return h('img', {
              src: data.src,
              alt: data.alt || data.title,
              title: data.title,
              class: data.className,
              style: {
                width: data.width,
                height: data.height,
                ...data.style,
              },
              onLoad: () => emit('load', data),
              onError: () =>
                emit('error', new Error('Image load failed'), data),
            })

          case 'video':
            return h('video', {
              src: data.src,
              controls: true,
              class: data.className,
              style: {
                width: data.width,
                height: data.height,
                ...data.style,
              },
              onLoadeddata: () => emit('load', data),
              onError: () =>
                emit('error', new Error('Video load failed'), data),
            })

          case 'component':
            if (data.component) {
              const component =
                typeof data.component === 'string'
                  ? props.options.components?.[data.component] ||
                    resolveComponent(data.component)
                  : data.component

              return h(component, {
                ...data.props,
                class: data.className,
                style: data.style,
              })
            }
            break

          default:
            return h(
              'div',
              {
                class: data.className,
                style: data.style,
              },
              data.content || `Unknown type: ${data.type}`
            )
        }
      } catch (error) {
        emit('error', error as Error, data)
        return h('div', { class: 'ne-material-render__error' }, 'Render error')
      }
    }

    return () => renderMaterial(props.data)
  },
})

// 事件处理
const handleRender = (data: NeMaterialData) => {
  emit('render', data)
}

const handleError = (error: Error, data: NeMaterialData) => {
  emit('error', error, data)
}

const handleLoad = (data: NeMaterialData) => {
  emit('load', data)
}

const handleClick = (event: Event, data: NeMaterialData) => {
  emit('click', event, data)
}
</script>

<style scoped>
.ne-material-render {
  width: 100%;
}

.ne-material-render--animated .ne-material-render__item {
  transition: all var(--ne-animation-duration, 300ms) ease;
}

.ne-material-render__item {
  margin-bottom: 8px;
}

.ne-material-render__item:last-child {
  margin-bottom: 0;
}

.ne-material-render__error {
  padding: 8px 12px;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  color: #721c24;
  font-size: 14px;
}
</style>
