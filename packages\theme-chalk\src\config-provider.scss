@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(config-provider) {
  width: 100%;
  height: 100%;

  @include m(dark) {
    color-scheme: dark;
  }

  @include m(strict) {
    // 严格模式下的样式
  }
}

// 暗黑模式全局样式
.dark {
  color-scheme: dark;
  
  // 可以在这里添加暗黑模式的全局样式覆盖
  --ne-color-primary: #409eff;
  --ne-color-success: #67c23a;
  --ne-color-warning: #e6a23c;
  --ne-color-danger: #f56c6c;
  --ne-color-info: #909399;
  
  // 背景色
  --ne-bg-color: #141414;
  --ne-bg-color-page: #0a0a0a;
  --ne-bg-color-overlay: #1d1e1f;
  
  // 文字颜色
  --ne-text-color-primary: rgba(255, 255, 255, 0.85);
  --ne-text-color-regular: rgba(255, 255, 255, 0.65);
  --ne-text-color-secondary: rgba(255, 255, 255, 0.45);
  --ne-text-color-placeholder: rgba(255, 255, 255, 0.25);
  
  // 边框颜色
  --ne-border-color: rgba(255, 255, 255, 0.15);
  --ne-border-color-light: rgba(255, 255, 255, 0.1);
  --ne-border-color-lighter: rgba(255, 255, 255, 0.05);
  --ne-border-color-extra-light: rgba(255, 255, 255, 0.02);
}
