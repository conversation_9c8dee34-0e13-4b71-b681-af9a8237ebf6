import { checkboxGroupProps, checkboxProps } from 'element-plus'
import { buildProps } from '@neue-plus/utils'
import type { ExtractPropTypes } from 'vue'

// 选项配置接口
export const neCheckboxProps = {
  ...checkboxProps,
}
export type NeCheckboxProps = ExtractPropTypes<typeof neCheckboxProps>
// 继承 Element Plus CheckboxGroup 的所有属性，并添加自定义属性
export const neCheckboxGroupProps = buildProps({
  // 继承 Element Plus CheckboxGroup 的所有属性
  ...checkboxGroupProps,
  // 自定义属性：选项配置数组
  options: {
    type: Array as () => Array<NeCheckboxProps>,
    default: () => [],
  },
} as const)

export type NeCheckboxGroupProps = ExtractPropTypes<typeof neCheckboxGroupProps>

// 事件类型定义
export type NeCheckboxGroupEmits = {
  (e: 'update:modelValue', value: any): void
  (e: 'change', value: any): void
}
