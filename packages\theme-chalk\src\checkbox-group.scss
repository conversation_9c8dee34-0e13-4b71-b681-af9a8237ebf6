@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(checkbox-group) {
  display: flex;
  flex-wrap: wrap;
  gap: getCssVar('checkbox-group', 'gap');

  @include e(item) {
    margin-right: 0;
    margin-bottom: 0;
  }

  // 垂直布局
  @include m(vertical) {
    flex-direction: column;

    .#{$namespace}-checkbox {
      margin-right: 0;
      margin-bottom: getCssVar('checkbox-group', 'item-margin-bottom');

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  // 边框样式
  @include m(bordered) {
    .#{$namespace}-checkbox {
      margin-right: 0;
      margin-bottom: getCssVar('checkbox-group', 'item-margin-bottom');

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  // 水平布局时的间距
  &:not(.#{$namespace}-checkbox-group--vertical) {
    .#{$namespace}-checkbox {
      margin-right: getCssVar('checkbox-group', 'item-margin-right');
      margin-bottom: 0;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  // 禁用状态
  @include when(disabled) {
    .#{$namespace}-checkbox {
      cursor: not-allowed;
    }
  }

  // 大小变体
  @include m(large) {
    gap: getCssVar('checkbox-group', 'gap-large');
  }

  @include m(small) {
    gap: getCssVar('checkbox-group', 'gap-small');
  }
}
