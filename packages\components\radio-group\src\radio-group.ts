import { radioGroupProps } from 'element-plus'
import { buildProps } from '@neue-plus/utils'
import type { ExtractPropTypes } from 'vue'

// 选项配置接口
export interface NeRadioGroupOption {
  label: string
  value: any
  disabled?: boolean
  border?: boolean
  size?: 'large' | 'default' | 'small'
}

// 继承 Element Plus RadioGroup 的所有属性，并添加自定义属性
export const neRadioGroupProps = buildProps({
  // 继承 Element Plus RadioGroup 的所有属性
  ...radioGroupProps,
  
  // 自定义属性：选项配置数组
  options: {
    type: Array as () => Array<NeRadioGroupOption>,
    default: () => [],
  },
  
  // 自定义属性：是否显示边框
  bordered: {
    type: Boolean,
    default: false,
  },
  
  // 自定义属性：选项之间的间距
  gap: {
    type: [String, Number],
    default: '12px',
  },
  
  // 自定义属性：布局方向
  direction: {
    type: String as () => 'horizontal' | 'vertical',
    default: 'horizontal',
  },
} as const)

export type NeRadioGroupProps = ExtractPropTypes<typeof neRadioGroupProps>

// 事件类型定义
export type NeRadioGroupEmits = {
  (e: 'update:modelValue', value: any): void
  (e: 'change', value: any): void
}
