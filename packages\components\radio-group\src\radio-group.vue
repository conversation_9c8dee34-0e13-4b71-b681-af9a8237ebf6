<template>
  <el-radio-group
    v-bind="$attrs"
    :model-value="modelValue"
    :disabled="disabled"
    :size="size"
    :text-color="textColor"
    :fill="fill"
    :validate-event="validateEvent"
    :class="radioGroupClass"
    :style="radioGroupStyle"
    @update:model-value="handleUpdateModelValue"
    @change="handleChange"
  >
    <!-- 通过 options 配置生成的选项 -->
    <el-radio
      v-for="option in options"
      :key="option.value"
      :value="option.value"
      :disabled="option.disabled || disabled"
      :border="option.border || bordered"
      :size="option.size || size"
    >
      {{ option.label }}
    </el-radio>

    <!-- 通过 slot 传入的选项 -->
    <slot />
  </el-radio-group>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { type NeRadioGroupEmits, neRadioGroupProps } from './radio-group'

defineOptions({
  name: 'NeRadioGroup',
  inheritAttrs: false,
})

const props = defineProps(neRadioGroupProps)
const emit = defineEmits<NeRadioGroupEmits>()

// 计算样式类
const radioGroupClass = computed(() => {
  return [
    'ne-radio-group',
    {
      'ne-radio-group--vertical': props.direction === 'vertical',
      'ne-radio-group--bordered': props.bordered,
    },
  ]
})

// 计算样式
const radioGroupStyle = computed(() => {
  const styles: Record<string, any> = {}

  if (props.direction === 'vertical') {
    styles.flexDirection = 'column'
    styles.gap = props.gap
  } else {
    styles.gap = props.gap
  }

  return styles
})

// 处理值更新
const handleUpdateModelValue = (value: any) => {
  emit('update:modelValue', value)
}

// 处理变化事件
const handleChange = (value: any) => {
  emit('change', value)
}
</script>

<style scoped>
.ne-radio-group {
  display: flex;
  flex-wrap: wrap;
}

.ne-radio-group--vertical {
  flex-direction: column;
}

.ne-radio-group--bordered .el-radio {
  margin-right: 0;
  margin-bottom: 8px;
}

.ne-radio-group--vertical .el-radio {
  margin-right: 0;
  margin-bottom: 8px;
}

.ne-radio-group--vertical .el-radio:last-child {
  margin-bottom: 0;
}
</style>
