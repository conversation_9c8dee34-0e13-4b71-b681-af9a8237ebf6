import { buildProps } from '@neue-plus/utils'
import type { TableColumnCtx } from 'element-plus'
import type { ExtractPropTypes } from 'vue'

export type ValueEnumStatus =
  | 'Success'
  | 'Error'
  | 'Processing'
  | 'Default'
  | 'Warning'
export interface ValueEnumItem {
  text: string
  status?: ValueEnumStatus
  color?: string
  disabled?: boolean
}

export type ValueEnum = Record<string, ValueEnumItem>

export type FilterTypeEnum = 'input' | 'select' | 'date' | 'daterange'

// 继承Element Plus的TableColumnCtx，只添加自定义属性
export const neTableColumnProps = buildProps({
  type: {
    type: String,
    default: 'default',
  },
  label: String,
  prop: String,
  width: {
    type: [String, Number],
    default: '',
  },
  minWidth: {
    type: [String, Number],
    default: '',
  },
  sortable: {
    type: [Boolean, String],
    default: false,
  },
  resizable: {
    type: Boolean,
    default: true,
  },
  showOverflowTooltip: {
    type: Boolean,
    default: false,
  },
  align: String,
  // 自定义属性：值枚举
  valueEnum: {
    type: Object as () => ValueEnum,
  },
  filterType: {
    type: String as () => FilterTypeEnum,
    default: 'input',
  },
  filterPlaceholder: {
    type: String,
    default: '请输入',
  },
  hideInTable: {
    type: Boolean,
    default: false,
  },
} as const)

export type NeTableColumnProps = ExtractPropTypes<typeof neTableColumnProps> &
  Partial<TableColumnCtx<any>>
