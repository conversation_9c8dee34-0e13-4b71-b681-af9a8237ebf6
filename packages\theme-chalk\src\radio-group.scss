@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(radio-group) {
  display: flex;
  flex-wrap: wrap;
  gap: getCssVar('radio-group', 'gap');

  @include e(item) {
    margin-right: 0;
    margin-bottom: 0;
  }

  // 垂直布局
  @include m(vertical) {
    flex-direction: column;
    
    .#{$namespace}-radio {
      margin-right: 0;
      margin-bottom: getCssVar('radio-group', 'item-margin-bottom');
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  // 边框样式
  @include m(bordered) {
    .#{$namespace}-radio {
      margin-right: 0;
      margin-bottom: getCssVar('radio-group', 'item-margin-bottom');
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  // 水平布局时的间距
  &:not(.#{$namespace}-radio-group--vertical) {
    .#{$namespace}-radio {
      margin-right: getCssVar('radio-group', 'item-margin-right');
      margin-bottom: 0;
      
      &:last-child {
        margin-right: 0;
      }
    }
  }

  // 禁用状态
  @include when(disabled) {
    .#{$namespace}-radio {
      cursor: not-allowed;
    }
  }

  // 大小变体
  @include m(large) {
    gap: getCssVar('radio-group', 'gap-large');
  }

  @include m(small) {
    gap: getCssVar('radio-group', 'gap-small');
  }
}
