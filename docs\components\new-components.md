# 新增组件文档

本文档介绍了新增的三个核心组件：NeConfigProvider、NeMaterialRender 和 NeRenderCore。

## NeConfigProvider 配置提供者

### 概述
NeConfigProvider 是一个全局配置组件，用于为整个应用或应用的某个部分提供统一的配置，包括主题、语言、尺寸等。

### 特性
- 🎨 主题配置（颜色、字体、边框等）
- 🌙 暗黑模式支持
- 🌍 国际化配置
- 📏 全局尺寸设置
- 🔧 命名空间自定义
- ⚡ 严格模式

### 基础用法
```vue
<template>
  <NeConfigProvider :theme="themeConfig" :dark="isDark">
    <YourApp />
  </NeConfigProvider>
</template>

<script setup>
import { ref } from 'vue'

const isDark = ref(false)
const themeConfig = ref({
  primaryColor: '#ff6b6b',
  successColor: '#51cf66',
  borderRadius: '8px',
})
</script>
```

### API

#### Props
| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| locale | 语言配置 | `Language` | - |
| size | 全局尺寸 | `'large' \| 'default' \| 'small'` | `'default'` |
| theme | 主题配置 | `NeThemeConfig` | `{}` |
| dark | 是否开启暗黑模式 | `boolean` | `false` |
| disabled | 全局禁用状态 | `boolean` | `false` |
| zIndex | z-index 基础值 | `number` | `2000` |
| namespace | 命名空间前缀 | `string` | `'ne'` |
| strict | 是否开启严格模式 | `boolean` | `false` |

## NeMaterialRender 材料渲染器

### 概述
NeMaterialRender 是一个通用的材料渲染组件，可以根据配置动态渲染不同类型的内容，如文本、图片、视频、组件等。

### 特性
- 📝 多种材料类型支持（文本、图片、视频、音频、iframe、组件、HTML、Markdown）
- 🔒 安全模式（防止 XSS 攻击）
- 🎬 动画支持
- 🔄 懒加载
- 🎨 自定义渲染器
- 🛠️ 错误处理

### 基础用法
```vue
<template>
  <NeMaterialRender 
    :data="materialData" 
    :safe="true"
    :animated="true"
    @render="handleRender"
    @error="handleError"
  />
</template>

<script setup>
import { ref } from 'vue'

const materialData = ref([
  {
    type: 'text',
    content: '这是文本内容',
    className: 'text-primary'
  },
  {
    type: 'image',
    src: 'https://example.com/image.jpg',
    alt: '示例图片',
    width: '300px'
  },
  {
    type: 'component',
    component: 'el-button',
    props: { type: 'primary' },
    children: [{ type: 'text', content: '按钮' }]
  }
])

const handleRender = (data) => {
  console.log('渲染:', data)
}

const handleError = (error, data) => {
  console.error('渲染错误:', error, data)
}
</script>
```

### API

#### Props
| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| data | 材料数据 | `NeMaterialData \| NeMaterialData[]` | - |
| options | 渲染选项 | `NeRenderOptions` | `{}` |
| safe | 是否启用安全模式 | `boolean` | `true` |
| lazy | 是否启用懒加载 | `boolean` | `false` |
| tag | 自定义标签名 | `string` | `'div'` |
| animated | 是否启用动画 | `boolean` | `false` |
| animationDuration | 动画持续时间 | `number` | `300` |

#### Events
| 事件名 | 说明 | 参数 |
|--------|------|------|
| render | 渲染时触发 | `(data: NeMaterialData)` |
| error | 渲染错误时触发 | `(error: Error, data: NeMaterialData)` |
| load | 资源加载完成时触发 | `(data: NeMaterialData)` |
| click | 点击时触发 | `(event: Event, data: NeMaterialData)` |

## NeRenderCore 渲染核心

### 概述
NeRenderCore 是一个底层的动态渲染组件，可以根据节点配置动态生成 Vue 虚拟节点，支持条件渲染、循环渲染、组件渲染等高级功能。

### 特性
- 🎯 动态节点渲染
- 🔀 条件渲染（if/show）
- 🔄 循环渲染
- 🧩 组件渲染
- 🎨 自定义指令支持
- 🐛 调试模式
- ⚡ 严格模式
- 🔧 渲染钩子

### 基础用法
```vue
<template>
  <NeRenderCore 
    :nodes="renderNodes" 
    :debug="true"
    :config="renderConfig"
    @render="handleRender"
    @error="handleError"
  />
</template>

<script setup>
import { ref } from 'vue'

const renderNodes = ref([
  {
    type: 'element',
    tag: 'div',
    className: 'container',
    children: [
      {
        type: 'element',
        tag: 'h1',
        children: [{ type: 'text', text: '动态标题' }]
      },
      {
        type: 'component',
        component: 'el-button',
        props: { type: 'primary' },
        events: { click: () => console.log('clicked') },
        children: [{ type: 'text', text: '动态按钮' }]
      }
    ]
  }
])

const renderConfig = ref({
  strict: false,
  components: {
    // 自定义组件映射
  },
  beforeRender: (node) => {
    // 渲染前处理
    return node
  }
})
</script>
```

### API

#### Props
| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| nodes | 渲染节点数据 | `NeRenderNode \| NeRenderNode[]` | - |
| config | 渲染配置 | `NeRenderConfig` | `{}` |
| tag | 根元素标签 | `string` | `'div'` |
| strict | 是否启用严格模式 | `boolean` | `false` |
| debug | 是否启用调试模式 | `boolean` | `false` |

#### Events
| 事件名 | 说明 | 参数 |
|--------|------|------|
| render | 渲染时触发 | `(node: NeRenderNode, vnode: VNode)` |
| error | 渲染错误时触发 | `(error: Error, node: NeRenderNode)` |
| beforeRender | 渲染前触发 | `(node: NeRenderNode)` |
| afterRender | 渲染后触发 | `(vnode: VNode, node: NeRenderNode)` |

## 样式定制

所有组件都支持通过 CSS 变量进行样式定制，并且已经集成到主题系统中。

### CSS 变量
```css
:root {
  /* ConfigProvider */
  --ne-color-primary: #409eff;
  --ne-border-radius: 4px;
  
  /* MaterialRender */
  --ne-material-render-item-margin-bottom: 8px;
  --ne-animation-duration: 300ms;
  
  /* RenderCore */
  --ne-render-core-error-padding: 8px 12px;
  --ne-render-core-debug-badge-padding: 2px 4px;
}
```

## 最佳实践

1. **ConfigProvider** 应该放在应用的最外层，为整个应用提供统一配置
2. **MaterialRender** 适用于内容管理系统、文档渲染等场景
3. **RenderCore** 适用于低代码平台、动态表单等需要高度定制的场景
4. 在生产环境中建议开启安全模式，防止 XSS 攻击
5. 使用调试模式可以帮助开发时定位渲染问题

## 注意事项

- 所有组件都继承了 Element Plus 的设计规范
- 支持 TypeScript，提供完整的类型定义
- 兼容 Vue 3 Composition API
- 支持服务端渲染（SSR）
