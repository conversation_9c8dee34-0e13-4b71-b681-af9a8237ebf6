<template>
  <div class="ne-pro-table">
    <!-- 搜索表单 - 使用Dynamic Form -->
    <div v-if="showFilter && (searchConfig || filterColumns.length > 0)">
      <ne-dynamic-form
        v-model="filters"
        :config="finalSearchConfig"
        @submit="handleSearch"
        @reset="handleReset"
        @change="handleFilterChange"
      >
        <template #extra-buttons>
          <el-button @click="handleRefresh">刷新</el-button>
        </template>
      </ne-dynamic-form>
    </div>

    <!-- 表格 -->
    <div class="ne-pro-table__table">
      <el-table
        v-bind="$attrs"
        :data="data"
        :loading="loading"
        :size="size"
        :class="{ 'ne-table--bordered': bordered }"
        @sort-change="handleSortChange"
      >
        <el-table-column
          v-for="column in visibleColumns"
          :key="column.prop || column.label"
          v-bind="column"
        />
        <slot />
      </el-table>
    </div>

    <!-- 分页 -->
    <div v-if="showPagination" class="ne-pro-table__pagination">
      <el-pagination
        v-bind="pagination"
        :current-page="pagination.current"
        :page-size="pagination.pageSize"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from 'vue'
import { keys } from 'lodash-unified'
import NeDynamicForm from '../../dynamic-form'
import { type ProTableFilters, proTableProps } from './pro-table'
import type {
  DynamicFormConfig,
  DynamicFormItem,
} from '../../dynamic-form/src/dynamic-form'

defineOptions({
  name: 'NeProTable',
  inheritAttrs: false,
})

const props = defineProps(proTableProps)
console.log('proTableProps', props)
const emit = defineEmits([
  'filter-change',
  'page-change',
  'sort-change',
  'refresh',
])

// 筛选数据
const filters = ref<ProTableFilters>({})

// 可筛选的列
const filterColumns = computed(() => {
  return props.columns.filter(
    (column) => column.filterable && !column.hideInTable
  )
})

// 可见的列
const visibleColumns = computed(() => {
  return props.columns.filter((column) => !column.hideInTable)
})

// 最终的搜索表单配置
const finalSearchConfig = computed((): DynamicFormConfig => {
  // 如果提供了自定义搜索配置，直接使用
  if (props.searchConfig) {
    return {
      layout: 'inline',
      ...props.searchConfig,
    }
  }

  // 否则根据columns自动生成搜索表单配置
  const items: DynamicFormItem[] = filterColumns.value.map((column) => {
    const item: DynamicFormItem = {
      prop: column.prop!,
      label: column.label!,
      type: getFormItemType(column.filterType!),
      placeholder: column.filterPlaceholder || `请输入${column.label}`,
      clearable: true,
    }

    // 设置选项
    if (column.valueEnum) {
      const { valueEnum } = column
      item.options = keys(valueEnum).map((item) => {
        return {
          label: valueEnum[item].text,
          value: item,
        }
      })
    }

    // 设置日期格式
    if (column.filterType === 'date') {
      item.format = 'YYYY-MM-DD'
      item.valueFormat = 'YYYY-MM-DD'
    } else if (column.filterType === 'daterange') {
      item.format = 'YYYY-MM-DD'
      item.valueFormat = 'YYYY-MM-DD'
    }

    return item
  })

  return {
    layout: 'inline',
    showSubmit: true,
    showReset: true,
    submitText: '搜索',
    resetText: '重置',
    items,
  }
})

// 将ProTable的filterType转换为DynamicForm的type
const getFormItemType = (filterType: string): DynamicFormItem['type'] => {
  const typeMap: Record<string, DynamicFormItem['type']> = {
    input: 'input',
    select: 'select',
    date: 'date',
    daterange: 'daterange',
  }
  return typeMap[filterType] || 'input'
}

// 初始化筛选表单
const initFilters = () => {
  const newFilters: ProTableFilters = {}
  filterColumns.value.forEach((column) => {
    if (column.prop) {
      newFilters[column.prop] = column.filterType === 'daterange' ? [] : ''
    }
  })
  filters.value = newFilters
}

// 筛选变化处理
const handleFilterChange = (prop?: string, value?: any) => {
  // 如果是Dynamic Form的change事件，直接触发
  if (prop && value !== undefined) {
    emit('filter-change', filters.value)
  } else {
    // 兼容原来的调用方式
    emit('filter-change', filters.value)
  }
}

// 搜索
const handleSearch = () => {
  emit('filter-change', filters.value)
}

// 重置筛选
const handleReset = () => {
  initFilters()
  emit('filter-change', filters.value)
}

// 刷新
const handleRefresh = () => {
  emit('refresh')
}

// 分页大小变化
const handleSizeChange = (pageSize: number) => {
  emit('page-change', 1, pageSize)
}

// 当前页变化
const handleCurrentChange = (current: number) => {
  emit('page-change', current, props.pagination.pageSize || 10)
}

// 排序变化
const handleSortChange = ({
  prop,
  order,
}: {
  prop: string
  order: 'ascending' | 'descending' | null
}) => {
  emit('sort-change', prop, order)
}

// 监听columns变化，重新初始化筛选表单
watch(
  () => props.columns,
  () => {
    initFilters()
  },
  { immediate: true }
)
</script>

<style scoped>
.ne-pro-table__filter {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 4px;
}

.ne-pro-table__pagination {
  margin-top: 16px;
  text-align: right;
}
</style>
