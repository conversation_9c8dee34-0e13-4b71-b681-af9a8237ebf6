<template>
  <div>
    <NeCard>
      <h3>Card组件测试</h3>
      <p>这是一个基于Element Plus封装的Card组件</p>
    </NeCard>

    <div class="mt-4">
      <h3>Table组件测试 - 使用Slot方式</h3>
      <NeTable :data="tableData">
        <el-table-column prop="name" label="姓名" width="180" />
        <el-table-column prop="age" label="年龄" width="180" />
        <el-table-column prop="address" label="地址" />
      </NeTable>
    </div>

    <div class="mt-4">
      <h3>Table组件测试 - 使用Columns配置</h3>
      <NeTable :data="tableData" :columns="tableColumns" />
    </div>

    <div class="mt-4">
      <h3>ProTable组件测试 - 自定义Dynamic Form搜索</h3>
      <NeProTable
        :data="currentPageData"
        :columns="proTableColumns"
        :search-config="proTableSearchConfig"
        :loading="loading"
        :pagination="pagination"
        @filter-change="handleFilterChange"
        @page-change="handlePageChange"
        @sort-change="handleSortChange"
        @refresh="handleRefresh"
      />
    </div>

    <div class="mt-4">
      <h3>ProTable组件测试 - 自动生成搜索表单</h3>
      <NeProTable
        :data="currentPageData"
        :columns="proTableColumns"
        :loading="loading"
        :pagination="pagination"
        @filter-change="handleFilterChange"
        @page-change="handlePageChange"
        @sort-change="handleSortChange"
        @refresh="handleRefresh"
      />
    </div>

    <div class="mt-4">
      <h3>Dynamic Form组件测试 - 动态表单</h3>
      <NeCard>
        <NeDynamicForm
          v-model="formData"
          :config="formConfig"
          :loading="formLoading"
          @submit="handleFormSubmit"
          @reset="handleFormReset"
          @change="handleFormChange"
        />
      </NeCard>

      <div class="mt-4">
        <h4>表单数据：</h4>
        <pre>{{ JSON.stringify(formData, null, 2) }}</pre>
      </div>
    </div>

    <div class="mt-4">
      <h3>RadioGroup组件测试 - 基础用法</h3>
      <NeCard>
        <h4>使用 options 配置：</h4>
        <NeRadioGroup
          v-model="radioValue1"
          :options="radioOptions"
          @change="handleRadioChange"
        />
        <p>选中值：{{ radioValue1 }}</p>

        <h4 class="mt-4">垂直布局：</h4>
        <NeRadioGroup
          v-model="radioValue2"
          :options="radioOptions"
          direction="vertical"
          gap="16px"
          @change="handleRadioChange"
        />
        <p>选中值：{{ radioValue2 }}</p>

        <h4 class="mt-4">边框样式：</h4>
        <NeRadioGroup
          v-model="radioValue3"
          :options="radioOptions"
          bordered
          @change="handleRadioChange"
        />
        <p>选中值：{{ radioValue3 }}</p>

        <h4 class="mt-4">使用插槽：</h4>
        <NeRadioGroup v-model="radioValue4" @change="handleRadioChange">
          <el-radio value="custom1">自定义选项1</el-radio>
          <el-radio value="custom2">自定义选项2</el-radio>
          <el-radio value="custom3" disabled>禁用选项</el-radio>
        </NeRadioGroup>
        <p>选中值：{{ radioValue4 }}</p>
      </NeCard>
    </div>

    <div class="mt-4">
      <h3>ConfigProvider组件测试</h3>
      <NeCard>
        <h4>主题配置：</h4>
        <NeConfigProvider :theme="themeConfig" :dark="isDark">
          <div
            style="
              padding: 16px;
              border: 1px solid var(--ne-color-primary, #409eff);
              border-radius: var(--ne-border-radius, 4px);
            "
          >
            <p>这是在 ConfigProvider 内的内容</p>
            <el-button type="primary">主色按钮</el-button>
            <el-button type="success">成功按钮</el-button>
          </div>
        </NeConfigProvider>

        <div class="mt-4">
          <el-switch v-model="isDark" active-text="暗黑模式" />
        </div>
      </NeCard>
    </div>

    <div class="mt-4">
      <h3>MaterialRender组件测试</h3>
      <NeCard>
        <h4>使用 elements 和 config 方式：</h4>
        <NeMaterialRender
          :elements="materialElements"
          :config="materialConfig"
          @render="handleMaterialRender"
          @error="handleMaterialError"
          @before-render="handleBeforeRender"
          @after-render="handleAfterRender"
        />

        <h4 class="mt-4">传统 data 方式：</h4>
        <NeMaterialRender
          :data="materialData"
          @render="handleMaterialRender"
          @error="handleMaterialError"
        />
      </NeCard>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, computed } from 'vue'
import type {
  ProTableColumn,
  ProTableFilters,
} from '@neue-plus/components/pro-table'
import type { DynamicFormConfig } from '@neue-plus/components/dynamic-form'

const tableData = ref([
  {
    name: '张三',
    age: 25,
    address: '北京市朝阳区',
  },
  {
    name: '李四',
    age: 30,
    address: '上海市浦东新区',
  },
  {
    name: '王五',
    age: 28,
    address: '广州市天河区',
  },
  {
    name: '赵六',
    age: 32,
    address: '深圳市南山区',
  },
])

// 表格列配置 - 继承el-table-column的所有属性
const tableColumns = ref([
  {
    prop: 'name',
    label: '姓名',
    fixed: 'left',
  },
  {
    prop: 'age',
    label: '年龄',
    sortable: true,
  },
  {
    prop: 'address',
    label: '地址',
  },
])

// ProTable相关数据
const loading = ref(false)
const filters = ref<ProTableFilters>({})
const pagination = ref({
  current: 1,
  pageSize: 10,
  total: 0,
})

// 扩展数据用于ProTable测试
const allProTableData = ref([
  {
    name: '张三',
    age: 25,
    address: '北京市朝阳区',
    status: 'active',
    createTime: '2024-01-01',
  },
  {
    name: '李四',
    age: 30,
    address: '上海市浦东新区',
    status: 'inactive',
    createTime: '2024-01-02',
  },
  {
    name: '王五',
    age: 28,
    address: '广州市天河区',
    status: 'active',
    createTime: '2024-01-03',
  },
  {
    name: '赵六',
    age: 32,
    address: '深圳市南山区',
    status: 'pending',
    createTime: '2024-01-04',
  },
  {
    name: '钱七',
    age: 26,
    address: '杭州市西湖区',
    status: 'active',
    createTime: '2024-01-05',
  },
  {
    name: '孙八',
    age: 29,
    address: '南京市鼓楼区',
    status: 'inactive',
    createTime: '2024-01-06',
  },
  {
    name: '周九',
    age: 31,
    address: '武汉市洪山区',
    status: 'active',
    createTime: '2024-01-07',
  },
  {
    name: '吴十',
    age: 27,
    address: '成都市锦江区',
    status: 'pending',
    createTime: '2024-01-08',
  },
])

// ProTable列配置 - 包含筛选功能
const proTableColumns = ref<ProTableColumn[]>([
  {
    prop: 'name',
    label: '姓名',
    width: 120,
    filterType: 'input',
    filterPlaceholder: '请输入姓名',
  },
  {
    prop: 'age',
    label: '年龄',
    width: 100,
    sortable: true,
    align: 'center',
  },
  {
    prop: 'status',
    label: '状态',
    width: 120,
    filterType: 'select',
    valueEnum: {
      active: {
        text: '激活',
        status: 'Success',
      },
      inactive: {
        text: '未激活',
        status: 'Error',
      },
      pending: {
        text: '待审核',
        status: 'Processing',
      },
    },
  },
  {
    prop: 'createTime',
    label: '创建时间',
    width: 150,
    filterable: true,
    filterType: 'date',
  },
  {
    prop: 'address',
    label: '地址',
    showOverflowTooltip: true,
    minWidth: 200,
  },
])

// ProTable搜索表单配置 - 使用Dynamic Form
const proTableSearchConfig = ref<DynamicFormConfig>({
  layout: 'inline',
  items: [
    {
      prop: 'name',
      label: '姓名',
      type: 'input',
      placeholder: '请输入姓名',
      clearable: true,
      span: 12,
    },
    {
      prop: 'status',
      label: '状态',
      type: 'select',
      placeholder: '请选择状态',
      clearable: true,
      span: 12,
      options: [
        { label: '激活', value: 'active' },
        { label: '未激活', value: 'inactive' },
        { label: '待审核', value: 'pending' },
      ],
    },
    {
      prop: 'createTime',
      label: '创建时间',
      type: 'date',
      placeholder: '请选择创建时间',
      clearable: true,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  ],
})

// 计算当前页数据
const currentPageData = computed(() => {
  let filteredData = allProTableData.value

  // 应用筛选
  if (filters.value.name) {
    filteredData = filteredData.filter((item) =>
      item.name.includes(filters.value.name)
    )
  }
  if (filters.value.status) {
    filteredData = filteredData.filter(
      (item) => item.status === filters.value.status
    )
  }
  if (filters.value.createTime) {
    filteredData = filteredData.filter((item) =>
      item.createTime.includes(filters.value.createTime)
    )
  }

  // 更新总数
  pagination.value.total = filteredData.length

  // 分页
  const start = (pagination.value.current - 1) * pagination.value.pageSize
  const end = start + pagination.value.pageSize
  return filteredData.slice(start, end)
})

// 事件处理方法
const handleFilterChange = (newFilters: ProTableFilters) => {
  filters.value = newFilters
  pagination.value.current = 1 // 重置到第一页
  console.log('筛选变化:', newFilters)
}

const handlePageChange = (current: number, pageSize: number) => {
  pagination.value.current = current
  pagination.value.pageSize = pageSize
  console.log('分页变化:', { current, pageSize })
}

const handleSortChange = (
  prop: string,
  order: 'ascending' | 'descending' | null
) => {
  console.log('排序变化:', { prop, order })
  // 这里可以实现排序逻辑
}

const handleRefresh = () => {
  loading.value = true
  console.log('刷新数据')
  // 模拟刷新
  setTimeout(() => {
    loading.value = false
  }, 1000)
}

// Dynamic Form相关数据
const formLoading = ref(false)
const formData = ref({
  name: '',
  email: '',
  age: 18,
  gender: '',
  hobbies: [],
  birthday: '',
  isVip: false,
  city: '',
  description: '',
})

// Dynamic Form配置
const formConfig = ref<DynamicFormConfig>({
  layout: 'horizontal',
  labelWidth: '100px',
  gutter: 16,
  showSubmit: true,
  showReset: true,
  submitText: '提交表单',
  resetText: '重置表单',
  buttonAlign: 'center',
  items: [
    {
      prop: 'name',
      label: '姓名',
      type: 'input',
      required: true,
      placeholder: '请输入姓名',
      span: 12,
      rules: [
        { min: 2, max: 10, message: '姓名长度在2-10个字符', trigger: 'blur' },
      ],
    },
    {
      prop: 'email',
      label: '邮箱',
      type: 'input',
      required: true,
      placeholder: '请输入邮箱地址',
      span: 12,
      rules: [
        { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' },
      ],
    },
    {
      prop: 'age',
      label: '年龄',
      type: 'number',
      min: 1,
      max: 120,
      span: 12,
    },
    {
      prop: 'gender',
      label: '性别',
      type: 'radio',
      span: 12,
      options: [
        { label: '男', value: 'male' },
        { label: '女', value: 'female' },
      ],
    },
    {
      prop: 'hobbies',
      label: '爱好',
      type: 'checkbox',
      span: 12,
      options: [
        { label: '读书', value: 'reading' },
        { label: '运动', value: 'sports' },
        { label: '音乐', value: 'music' },
        { label: '旅行', value: 'travel' },
      ],
    },
    {
      prop: 'birthday',
      label: '生日',
      type: 'date',
      placeholder: '请选择生日',
      span: 12,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
    {
      prop: 'isVip',
      label: 'VIP会员',
      type: 'switch',
      span: 12,
    },
    {
      prop: 'city',
      label: '城市',
      type: 'select',
      placeholder: '请选择城市',
      span: 12,
      filterable: true,
      options: [
        { label: '北京', value: 'beijing' },
        { label: '上海', value: 'shanghai' },
        { label: '广州', value: 'guangzhou' },
        { label: '深圳', value: 'shenzhen' },
        { label: '杭州', value: 'hangzhou' },
      ],
    },
    {
      prop: 'description',
      label: '个人描述',
      type: 'textarea',
      placeholder: '请输入个人描述',
      span: 24,
      maxlength: 200,
      showWordLimit: true,
    },
  ],
})

// Dynamic Form事件处理
const handleFormSubmit = (data: Record<string, any>) => {
  formLoading.value = true
  console.log('表单提交:', data)

  // 模拟提交
  setTimeout(() => {
    formLoading.value = false
    alert('表单提交成功！')
  }, 1500)
}

const handleFormReset = () => {
  console.log('表单重置')
}

const handleFormChange = (prop: string, value: any) => {
  console.log('表单项变化:', { prop, value })
}

// RadioGroup相关数据
const radioValue1 = ref('option1')
const radioValue2 = ref('option2')
const radioValue3 = ref('')
const radioValue4 = ref('custom1')

// RadioGroup选项配置
const radioOptions = ref([
  { label: '选项一', value: 'option1' },
  { label: '选项二', value: 'option2' },
  { label: '选项三', value: 'option3' },
  { label: '禁用选项', value: 'option4', disabled: true },
])

// RadioGroup事件处理
const handleRadioChange = (value: any) => {
  console.log('RadioGroup值变化:', value)
}

// ConfigProvider相关数据
const isDark = ref(false)
const themeConfig = ref({
  primaryColor: '#ff6b6b',
  successColor: '#51cf66',
  warningColor: '#ffd43b',
  dangerColor: '#ff8787',
  borderRadius: '8px',
})

// MaterialRender相关数据
const materialData = ref([
  {
    id: 1,
    type: 'text',
    content: '这是一段文本内容',
    className: 'text-primary',
  },
  {
    id: 2,
    type: 'image',
    src: 'https://via.placeholder.com/300x200',
    alt: '示例图片',
    width: '300px',
    height: '200px',
  },
  {
    id: 3,
    type: 'component',
    component: 'el-button',
    props: {
      type: 'primary',
      onClick: () => console.log('按钮被点击'),
    },
    children: [{ type: 'text', content: '动态按钮' }],
  },
])

// MaterialRender elements 方式（使用新的 NeRenderCore 格式）
const materialElements = ref([
  {
    id: 'container',
    type: 'div',
    props: {
      class: 'demo-container',
      style: {
        padding: '16px',
        border: '1px solid #ddd',
        borderRadius: '4px',
        marginBottom: '8px',
      },
    },
    elements: [
      {
        id: 'title',
        type: 'h4',
        props: {
          style: { color: '#409eff', marginBottom: '12px' },
        },
        slots: {
          default: '通过新的 NeRenderCore 渲染的内容',
        },
      },
      {
        id: 'description',
        type: 'p',
        props: {
          style: { marginBottom: '16px' },
        },
        slots: {
          default: '这是使用新的 elements 配置格式的示例',
        },
      },
      {
        id: 'button',
        type: 'el-button',
        props: {
          type: 'primary',
          size: 'default',
        },
        events: [
          {
            name: 'click',
            handler: () => {
              console.log('按钮被点击了！')
              alert('Hello from NeRenderCore!')
            },
          },
        ],
        slots: {
          default: '点击我',
        },
      },
    ],
  },
])

// MaterialRender 配置
const materialConfig = ref({
  configProvider: {
    theme: {
      primaryColor: '#52c41a',
    },
  },
})

// RenderCore相关数据
const renderNodes = ref([
  {
    id: 1,
    type: 'element',
    tag: 'div',
    className: 'render-demo',
    style: { padding: '16px', border: '1px solid #ddd', borderRadius: '4px' },
    children: [
      {
        type: 'element',
        tag: 'h4',
        children: [{ type: 'text', text: '动态渲染的标题' }],
      },
      {
        type: 'element',
        tag: 'p',
        children: [{ type: 'text', text: '这是动态渲染的段落内容' }],
      },
      {
        type: 'component',
        component: 'el-button',
        props: {
          type: 'success',
          size: 'small',
        },
        children: [{ type: 'text', text: '动态组件' }],
      },
    ],
  },
])

// MaterialRender事件处理
const handleMaterialRender = (data: any) => {
  console.log('Material渲染:', data)
}

const handleMaterialError = (error: Error, data: any) => {
  console.error('Material渲染错误:', error, data)
}

const handleBeforeRender = (node: any) => {
  console.log('渲染前:', node)
}

const handleAfterRender = (vnode: any, node: any) => {
  console.log('渲染后:', vnode, node)
}

// RenderCore事件处理
const handleRenderCore = (node: any, vnode: any) => {
  console.log('RenderCore渲染:', node, vnode)
}

const handleRenderError = (error: Error, node: any) => {
  console.error('RenderCore渲染错误:', error, node)
}
</script>
<style scoped>
.mt-4 {
  margin-top: 1rem;
}
</style>
