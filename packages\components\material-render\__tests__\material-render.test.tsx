import { mount } from '@vue/test-utils'
import { describe, expect, test } from 'vitest'
import MaterialRender from '../src/material-render.vue'

describe('NeMaterialRender', () => {
  test('render text material', () => {
    const data = {
      type: 'text',
      content: 'Hello World',
      className: 'test-class',
    }

    const wrapper = mount(MaterialRender, {
      props: { data },
    })

    expect(wrapper.find('.ne-material-render').exists()).toBe(true)
    expect(wrapper.text()).toContain('Hello World')
  })

  test('render image material', () => {
    const data = {
      type: 'image',
      src: 'test.jpg',
      alt: 'Test Image',
      width: '100px',
      height: '100px',
    }

    const wrapper = mount(MaterialRender, {
      props: { data },
    })

    const img = wrapper.find('img')
    expect(img.exists()).toBe(true)
    expect(img.attributes('src')).toBe('test.jpg')
    expect(img.attributes('alt')).toBe('Test Image')
  })

  test('render array of materials', () => {
    const data = [
      { type: 'text', content: 'First item' },
      { type: 'text', content: 'Second item' },
    ]

    const wrapper = mount(MaterialRender, {
      props: { data },
    })

    expect(wrapper.findAll('.ne-material-render__item')).toHaveLength(2)
    expect(wrapper.text()).toContain('First item')
    expect(wrapper.text()).toContain('Second item')
  })

  test('emit render event', async () => {
    const data = {
      type: 'text',
      content: 'Test content',
    }

    const wrapper = mount(MaterialRender, {
      props: { data },
    })

    // 等待组件渲染完成
    await wrapper.vm.$nextTick()

    expect(wrapper.emitted('render')).toBeTruthy()
  })

  test('safe mode prevents HTML rendering', () => {
    const data = {
      type: 'html',
      content: '<script>alert("xss")</script>',
    }

    const wrapper = mount(MaterialRender, {
      props: {
        data,
        safe: true,
      },
    })

    expect(wrapper.text()).toContain('HTML content disabled in safe mode')
  })

  test('animated mode adds animation class', () => {
    const data = {
      type: 'text',
      content: 'Test content',
    }

    const wrapper = mount(MaterialRender, {
      props: {
        data,
        animated: true,
      },
    })

    expect(wrapper.find('.ne-material-render--animated').exists()).toBe(true)
  })
})
