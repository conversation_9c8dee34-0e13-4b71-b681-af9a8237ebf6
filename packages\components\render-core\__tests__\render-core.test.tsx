import { mount } from '@vue/test-utils'
import { describe, expect, test } from 'vitest'
import RenderCore from '../src/render-core.vue'

describe('NeRenderCore', () => {
  test('render element node', () => {
    const nodes = {
      type: 'element',
      tag: 'div',
      className: 'test-div',
      children: [
        {
          type: 'text',
          text: 'Hello World',
        },
      ],
    }

    const wrapper = mount(RenderCore, {
      props: { nodes },
    })

    expect(wrapper.find('.ne-render-core').exists()).toBe(true)
    expect(wrapper.find('.test-div').exists()).toBe(true)
    expect(wrapper.text()).toContain('Hello World')
  })

  test('render text node', () => {
    const nodes = {
      type: 'text',
      text: 'Simple text',
    }

    const wrapper = mount(RenderCore, {
      props: { nodes },
    })

    expect(wrapper.text()).toContain('Simple text')
  })

  test('render array of nodes', () => {
    const nodes = [
      {
        type: 'element',
        tag: 'p',
        children: [{ type: 'text', text: 'First paragraph' }],
      },
      {
        type: 'element',
        tag: 'p',
        children: [{ type: 'text', text: 'Second paragraph' }],
      },
    ]

    const wrapper = mount(RenderCore, {
      props: { nodes },
    })

    expect(wrapper.findAll('p')).toHaveLength(2)
    expect(wrapper.text()).toContain('First paragraph')
    expect(wrapper.text()).toContain('Second paragraph')
  })

  test('conditional rendering with if', () => {
    const nodes = {
      type: 'element',
      tag: 'div',
      if: false,
      children: [{ type: 'text', text: 'Should not render' }],
    }

    const wrapper = mount(RenderCore, {
      props: { nodes },
    })

    expect(wrapper.text()).not.toContain('Should not render')
  })

  test('conditional rendering with show', () => {
    const nodes = {
      type: 'element',
      tag: 'div',
      show: false,
      children: [{ type: 'text', text: 'Hidden content' }],
    }

    const wrapper = mount(RenderCore, {
      props: { nodes },
    })

    const hiddenDiv = wrapper.find('div[style*="display: none"]')
    expect(hiddenDiv.exists()).toBe(true)
  })

  test('debug mode adds debug class', () => {
    const nodes = {
      type: 'text',
      text: 'Debug test',
    }

    const wrapper = mount(RenderCore, {
      props: {
        nodes,
        debug: true,
      },
    })

    expect(wrapper.find('.ne-render-core--debug').exists()).toBe(true)
  })

  test('emit render event', async () => {
    const nodes = {
      type: 'text',
      text: 'Test content',
    }

    const wrapper = mount(RenderCore, {
      props: { nodes },
    })

    // 等待组件渲染完成
    await wrapper.vm.$nextTick()

    expect(wrapper.emitted('render')).toBeTruthy()
  })

  test('handle render error in strict mode', () => {
    const nodes = {
      type: 'component',
      component: 'NonExistentComponent',
    }

    expect(() => {
      mount(RenderCore, {
        props: {
          nodes,
          strict: true,
        },
      })
    }).toThrow()
  })
})
