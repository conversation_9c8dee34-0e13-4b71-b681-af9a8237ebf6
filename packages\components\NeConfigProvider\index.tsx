import { computed, defineComponent, h, onBeforeMount, onMounted, provide } from "vue";
import { ConfigProvider } from "ant-design-vue";
import type { ConfigProviderProps } from "ant-design-vue/es/config-provider";
import { provideComponentRegistry } from "@/hooks/useComponentRegistry";

const NeConfigProvider = defineComponent({
  name: "NeConfigProvider",
  props: {
    config: {
      type: Object as () => Partial<ConfigProviderProps>,
      required: false,
    },
  },
  setup(props, { slots }) {
    const registry = provideComponentRegistry();
    return {};
  },
  render() {
    return <ConfigProvider {...this.$props.config}>{{ ...this.$slots }}</ConfigProvider>;
  },
});

export default NeConfigProvider;
