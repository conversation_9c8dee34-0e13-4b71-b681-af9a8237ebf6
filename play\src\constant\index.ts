export const initialData = [
  { name: '<PERSON>', age: 24, address: 'New York' },
  { name: '<PERSON>', age: 30, address: 'Los Angeles' },
  { name: '<PERSON>', age: 28, address: 'Chicago' },
  { name: '<PERSON>', age: 24, address: 'New York' },
  { name: '<PERSON>', age: 30, address: 'Los Angeles' },
  { name: '<PERSON>', age: 28, address: 'Chicago' },
  { name: '<PERSON>', age: 24, address: 'New York' },
  { name: '<PERSON>', age: 30, address: 'Los Angeles' },
  { name: '<PERSON>', age: 28, address: 'Chicago' },
  { name: '<PERSON>', age: 24, address: 'New York' },
  { name: '<PERSON>', age: 30, address: 'Los Angeles' },
  { name: '<PERSON>', age: 28, address: 'Chicago' },
]
export const tableData = [
  {
    id: 1,
    number: 'ID00000021',
    name: 'Product4.1',
    ins_name: 'ID00000021',
    subcategory: 'NEUE ASM',
    version: 'V1.0',
    status: 'working',
    modified_by: '黄文隆',
    modified_time: Date.now(),
    date: '2016-05-02',
    address: 'No. 189, Grove St, Los Angeles',
  },
  {
    id: 2,
    number: 'ID00000021',
    name: 'Product4.1',
    ins_name: 'ID00000021',
    subcategory: 'NEUE ASM',
    version: 'V1.0',
    status: 'working',
    modified_by: '黄文隆',
    modified_time: Date.now() - 24 * 60 * 60 * 1000,
    date: '2016-05-04',
    address: 'No. 189, Grove St, Los Angeles',
  },
  {
    id: 3,
    number: 'ID00000021',
    name: 'Product4.1',
    ins_name: 'ID00000021',
    subcategory: 'NEUE ASM',
    version: 'V1.0',
    status: 'working',
    modified_by: '黄文隆',
    modified_time: Date.now(),
    date: '2016-05-02',
    address: 'No. 189, Grove St, Los Angeles',
    children: [
      {
        id: 31,
        number: 'ID00000021',
        name: 'Product4.1',
        ins_name: 'ID00000021',
        subcategory: 'NEUE ASM',
        version: 'V1.0',
        status: 'working',
        modified_by: '黄文隆',
        modified_time: Date.now(),
        date: '2016-05-02',
        address: 'No. 189, Grove St, Los Angeles',
      },
      {
        id: 32,
        number: 'ID00000021',
        name: 'Product4.1',
        ins_name: 'ID00000021',
        subcategory: 'NEUE ASM',
        version: 'V1.0',
        status: 'working',
        modified_by: '黄文隆',
        modified_time: Date.now(),
        date: '2016-05-02',
        address: 'No. 189, Grove St, Los Angeles',
      },
    ],
  },
  {
    id: 4,
    number: 'ID00000021',
    name: 'Product4.1',
    ins_name: 'ID00000021',
    subcategory: 'NEUE ASM',
    version: 'V1.0',
    status: 'working',
    modified_by: '黄文隆',
    modified_time: Date.now(),
    date: '2016-05-02',
    address: 'No. 189, Grove St, Los Angeles',
  },
]

export const pageData = {
  config: {
    configProvider: {},
    events: [],
    api: {},
  },
  events: {
    onBeforeMount: () => {
      console.log('NeConfigProvider 组件即将挂载')
    },
    onMounted: () => {
      console.log('NeConfigProvider 组件即将挂载')
    },
  },
  apis: {
    api: {
      sourceType: 'json',
      source: [],
    },
  },
  elements: [
    {
      id: 'SearchForm_60spo02i5g', // 组件唯一身份
      type: 'layout-content',
      name: '搜索表单',
      props: {},
      events: [],
      elements: [
        {
          id: 'div_3gx13gh2ht',
          type: 'div',
          name: 'div',
          props: {},
          elements: [
            {
              id: 'button_3gx13gh2ht',
              type: 'button',
              props: {},
              slots: {
                default: '在NeueCAX中打开',
              },
            },
            {
              id: 'button_expand_toggle',
              type: 'button',
              props: {},
              slots: {
                default: '展开/收缩',
              },
              events: [
                {
                  nickName: '展开收缩切换事件',
                  eventName: 'onClick',
                  actions: [
                    {
                      id: 'start',
                      type: 'start',
                      title: '开始',
                    },
                    {
                      id: '56132221', // 节点id
                      type: 'normal', // 节点类型
                      title: '切换展开收缩', // 节点名称
                      content: '智能切换树表格展开收缩状态', // 节点描述
                      config: {
                        actionType: 'toggleExpandAll', // 节点配置
                        actionName: '切换展开收缩',
                        target: 'MarsTable_3gx13gh2ht',
                      },
                      children: [],
                    },
                    {
                      id: 'end',
                      type: 'end',
                      title: '结束',
                    },
                  ],
                },
              ],
            },
          ],
        },
        {
          id: 'table_3gx13gh2ht',
          type: 'table',
          name: '普通表格',
          props: {
            columns: [
              {
                label: '零部件编号',
                prop: 'number',
              },
              {
                label: '零部件名称',
                prop: 'name',
              },
              {
                label: '子类别',
                prop: 'subcategory',
              },
            ],
            data: [],
          },
          elements: [],
          events: [
            {
              nickName: '行点击事件',
              eventName: 'onRowClick',
              actions: [
                {
                  id: 'start',
                  type: 'start',
                  title: '开始',
                },
                {
                  id: '56132221',
                  type: 'normal',
                  title: '节点7690',
                  content: '打开弹框',
                  config: {
                    actionType: 'openDrawer',
                    actionName: '打开drawer',
                    target: 'Drawer_er4mn6jbtk',
                  },
                  children: [],
                },
                {
                  id: 'end',
                  type: 'end',
                  title: '结束',
                },
              ],
            },
          ],
        },
      ],
    },
  ],
}
