<!-- eslint-disable @typescript-eslint/no-unused-vars -->
<template>
  <el-form
    ref="formRef"
    :model="formData"
    v-bind="attrs"
    @validate="handleValidate"
  >
    <el-row :gutter="config.gutter || 16">
      <el-col
        v-for="item in visibleItems"
        :key="item.prop"
        :span="item.span || 24"
        :offset="item.offset || 0"
      >
        <el-form-item
          :prop="item.prop"
          :label="item.label"
          :required="item.required"
          :rules="getItemRules(item)"
          v-bind="getFormItemProps(item)"
        >
          <!-- 自定义插槽 -->
          <slot
            v-if="item.slot"
            :name="item.slot"
            :item="item"
            :value="formData[item.prop]"
            :form-data="formData"
            @change="(value: any) => handleChange(item.prop, value)"
          />

          <!-- 自定义渲染 -->
          <component
            :is="item.render(formData)"
            v-else-if="item.render"
            v-model="formData[item.prop]"
            @change="(value: any) => handleChange(item.prop, value)"
          />

          <!-- 动态表单项 -->
          <component
            :is="getFormItemComponent(item.type)"
            v-else
            v-model="formData[item.prop]"
            v-bind="getFormItemAttrs(item)"
            :disabled="getItemDisabled(item)"
            @change="(value: any) => handleChange(item.prop, value)"
          >
            <!-- 选项渲染 -->
            <template
              v-if="['select', 'radio', 'checkbox'].includes(item.type)"
            >
              <component
                :is="getOptionComponent(item.type)"
                v-for="option in item.options"
                :key="option.value"
                :label="option.value"
                :disabled="option.disabled"
              >
                {{ option.label }}
              </component>
            </template>
          </component>
        </el-form-item>
      </el-col>
    </el-row>
    <!-- 操作按钮 -->
    <el-form-item
      v-if="showButtons"
      :style="{ textAlign: config.buttonAlign || 'left' }"
    >
      <el-button
        v-if="config.showSubmit"
        type="primary"
        :loading="loading"
        @click="handleSubmit"
      >
        {{ config.submitText || '提交' }}
      </el-button>

      <el-button v-if="config.showReset" @click="handleReset">
        {{ config.resetText || '重置' }}
      </el-button>

      <el-button v-if="config.showCancel" @click="handleCancel">
        {{ config.cancelText || '取消' }}
      </el-button>
      <template v-if="showButtons">
        <slot name="extra-buttons" />
      </template>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { computed, ref, useAttrs, watch } from 'vue'
import { type DynamicFormItem, dynamicFormProps } from './dynamic-form'

defineOptions({
  name: 'NeDynamicForm',
  inheritAttrs: false,
})

const props = defineProps(dynamicFormProps)
const attrs = useAttrs()

console.log('visibleItems', props, attrs)
const emit = defineEmits([
  'update:modelValue',
  'submit',
  'reset',
  'cancel',
  'change',
  'validate',
])

const formRef = ref()
const formData = ref<Record<string, any>>({})

// 可见的表单项
const visibleItems = computed(() => {
  console.log('visibleItems', props.config.items)
  return props.config.items.filter((item) => {
    if (typeof item.show === 'function') {
      return item.show(formData.value)
    }
    return item.show !== false
  })
})

// 是否显示按钮
const showButtons = computed(() => {
  return (
    props.config.showSubmit || props.config.showReset || props.config.showCancel
  )
})

// 获取表单项组件
const getFormItemComponent = (type: string) => {
  const componentMap: any = {
    input: 'el-input',
    textarea: 'el-input',
    select: 'el-select',
    radio: 'el-radio-group',
    checkbox: 'el-checkbox-group',
    switch: 'el-switch',
    date: 'el-date-picker',
    daterange: 'el-date-picker',
    time: 'el-time-picker',
    number: 'el-input-number',
    password: 'el-input',
    upload: 'el-upload',
    cascader: 'el-cascader',
    'tree-select': 'el-tree-select',
  }
  return componentMap[type] || 'el-input'
}

// 获取选项组件
const getOptionComponent = (type: string) => {
  const componentMap: any = {
    select: 'el-option',
    radio: 'el-radio',
    checkbox: 'el-checkbox',
  }
  return componentMap[type]
}

// 获取表单项属性
const getFormItemAttrs = (item: DynamicFormItem) => {
  const attrs: any = {
    placeholder: item.placeholder,
    clearable: item.clearable,
    multiple: item.multiple,
    filterable: item.filterable,
  }

  // 根据类型设置特定属性
  switch (item.type) {
    case 'textarea':
      attrs.type = 'textarea'
      attrs.rows = item.rows || 3
      break
    case 'password':
      attrs.type = 'password'
      attrs.showPassword = true
      break
    case 'number':
      attrs.min = item.min
      attrs.max = item.max
      attrs.step = item.step
      attrs.precision = item.precision
      break
    case 'date':
      attrs.type = 'date'
      attrs.format = item.format
      attrs.valueFormat = item.valueFormat
      break
    case 'daterange':
      attrs.type = 'daterange'
      attrs.format = item.format
      attrs.valueFormat = item.valueFormat
      break
    case 'upload':
      attrs.action = item.action
      attrs.accept = item.accept
      attrs.limit = item.limit
      break
    case 'cascader':
      attrs.options = item.options
      attrs.props = item.props
      break
  }

  // 移除undefined值
  Object.keys(attrs).forEach((key) => {
    if (attrs[key] === undefined) {
      delete attrs[key]
    }
  })

  return attrs
}

// 获取表单项props
const getFormItemProps = (item: DynamicFormItem) => {
  const {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    prop,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    label,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    type,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    show,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    disabled,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    options,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    render,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    slot,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    span,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    offset,
    ...rest
  } = item
  return rest
}

// 获取表单项规则
const getItemRules = (item: DynamicFormItem) => {
  const rules = [...(item.rules || [])]

  if (item.required) {
    rules.unshift({
      required: true,
      message: `请输入${item.label}`,
      trigger: ['blur', 'change'],
    })
  }

  return rules
}

// 获取表单项禁用状态
const getItemDisabled = (item: DynamicFormItem) => {
  if (props.disabled) return true
  if (typeof item.disabled === 'function') {
    return item.disabled(formData.value)
  }
  return item.disabled
}

// 处理值变化
const handleChange = (prop: string, value: any) => {
  formData.value[prop] = value
  emit('update:modelValue', formData.value)
  emit('change', prop, value)
}

// 处理验证
const handleValidate = (prop: string, isValid: boolean, message: string) => {
  emit('validate', prop, isValid, message)
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    emit('submit', formData.value)
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 重置表单
const handleReset = () => {
  formRef.value?.resetFields()
  emit('reset')
}

// 取消
const handleCancel = () => {
  emit('cancel')
}

// 暴露方法
defineExpose({
  validate: () => formRef.value?.validate(),
  resetFields: () => formRef.value?.resetFields(),
  clearValidate: () => formRef.value?.clearValidate(),
})

// 监听modelValue变化
watch(
  () => props.modelValue,
  (newValue) => {
    formData.value = { ...newValue }
  },
  { immediate: true, deep: true }
)

// 初始化表单数据
watch(
  () => props.config.items,
  (items) => {
    const newFormData = { ...formData.value }
    items.forEach((item) => {
      if (!(item.prop in newFormData)) {
        newFormData[item.prop] = getDefaultValue(item.type)
      }
    })
    formData.value = newFormData
  },
  { immediate: true }
)

// 获取默认值
const getDefaultValue = (type: string) => {
  switch (type) {
    case 'checkbox':
    case 'daterange':
      return []
    case 'switch':
      return false
    case 'number':
      return 0
    default:
      return ''
  }
}
</script>
