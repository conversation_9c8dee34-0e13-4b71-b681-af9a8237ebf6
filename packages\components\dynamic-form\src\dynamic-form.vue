<template>
  <el-form ref="formRef" :model="formData" v-bind="attrs">
    <el-row :gutter="config.gutter || 16">
      <el-col
        v-for="item in visibleItems"
        :key="item.prop"
        v-bind="item.colProps"
      >
        <el-form-item v-bind="item" :rules="getItemRules(item)">
          <!-- 自定义渲染 -->
          <component
            :is="getFormItemComponent(item.type)"
            v-bind="item"
            v-model="formData[item.prop]"
            @change="(value: any) => handleChange(item.prop, value)"
          />
        </el-form-item>
      </el-col>
    </el-row>
    <!-- 操作按钮 -->
    <el-form-item
      v-if="showButtons"
      :style="{ textAlign: config.buttonAlign || 'left' }"
    >
      <el-button
        v-if="config.showSubmit"
        type="primary"
        :loading="loading"
        @click="handleSubmit"
      >
        {{ config.submitText || '提交' }}
      </el-button>

      <el-button v-if="config.showReset" @click="handleReset">
        {{ config.resetText || '重置' }}
      </el-button>

      <el-button v-if="config.showCancel" @click="handleCancel">
        {{ config.cancelText || '取消' }}
      </el-button>
      <template v-if="showButtons">
        <slot name="extra-buttons" />
      </template>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { computed, defineExpose, ref, useAttrs, watch } from 'vue'
import { type DynamicFormItem, dynamicFormProps } from './dynamic-form'
import type { ComponentMap } from './dynamic-form'

defineOptions({
  name: 'NeDynamicForm',
  inheritAttrs: false,
})

const props = defineProps(dynamicFormProps)
const attrs = useAttrs()

const emit = defineEmits([
  'update:modelValue',
  'submit',
  'reset',
  'cancel',
  'change',
  'validate',
])

const formRef = ref()
const formData = ref<Record<string, any>>({})

// 可见的表单项
const visibleItems = computed(() => {
  return props.config.items
    .filter((item) => item.show !== false)
    .map((item) => {
      const { span = 24, offset = 0, ...restProps } = item
      return {
        colProps: { span, offset },
        ...restProps,
      }
    })
})

// 是否显示按钮
const showButtons = computed(() => {
  return (
    props.config.showSubmit || props.config.showReset || props.config.showCancel
  )
})

// 获取表单项组件
const getFormItemComponent = (type: string) => {
  const componentMap: ComponentMap = {
    input: 'el-input',
    textarea: 'el-input',
    select: 'el-select',
    radio: 'el-radio-group',
    checkbox: 'el-checkbox-group',
    switch: 'el-switch',
    date: 'el-date-picker',
    daterange: 'el-date-picker',
    time: 'el-time-picker',
    number: 'el-input-number',
    password: 'el-input',
    upload: 'el-upload',
    cascader: 'el-cascader',
    'tree-select': 'el-tree-select',
  }
  return componentMap[type as keyof ComponentMap] || 'el-input'
}

// 获取表单项规则
const getItemRules = (item: DynamicFormItem) => {
  const rules = [...(item.rules || [])]

  if (item.required) {
    rules.unshift({
      required: true,
      message: `请输入${item.label}`,
      trigger: ['blur', 'change'],
    })
  }
  return rules
}

// 处理值变化
const handleChange = (prop: string, value: any) => {
  console.log(prop, value)
  formData.value[prop] = value
  emit('update:modelValue', formData.value)
  emit('change', prop, value)
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    emit('submit', formData.value)
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 重置表单
const handleReset = () => {
  formRef.value?.resetFields()
  emit('reset')
}

// 取消
const handleCancel = () => {
  emit('cancel')
}

watch(
  () => props.modelValue,
  (newValue) => {
    formData.value = { ...formData.value, ...newValue }
  },
  { immediate: true, deep: true }
)

// 初始化表单数据
watch(
  () => props.config.items,
  (items) => {
    const newFormData = { ...formData.value }
    items.forEach((item) => {
      if (!(item.prop in newFormData)) {
        newFormData[item.prop] = getDefaultValue(item.type)
      }
    })
    formData.value = newFormData
  },
  { immediate: true }
)

// 获取默认值
const getDefaultValue = (type: string) => {
  switch (type) {
    case 'checkbox':
    case 'daterange':
      return []
    case 'switch':
      return false
    case 'number':
      return 0
    default:
      return ''
  }
}
// 暴露方法
defineExpose({
  validate: () => formRef.value?.validate(),
  resetFields: () => formRef.value?.resetFields(),
  clearValidate: () => formRef.value?.clearValidate(),
})
</script>
