// 单个事件动作节点
export interface ActionNode {
  id: string
  type: 'start' | 'end' | 'normal'
  title: string // 事件标题
  content?: string // 事件描述
  config?: {
    // 事件类型，如click、change、submit等
    actionType: string // 事件名称，如打开弹框、关闭弹框、提交表单等
    actionName?: string // 事件触发的方法名
    target?: string // 事件触发的目标元素
    [key: string]: any
  }
  children?: ActionNode[]
}

// 元素绑定的事件
export interface ElementEvent {
  nickName: string
  eventName: string
  actions: ActionNode[]
}

// 页面钩子事件
export interface PageEvents {
  onBeforeMount?: () => void
  onMounted?: () => void
}
