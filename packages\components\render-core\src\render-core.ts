import { buildProps, buildProp } from '@neue-plus/utils'
import type { ExtractPropTypes, VNode, Component } from 'vue'

// 渲染节点类型
export type NeRenderNodeType = 
  | 'element'
  | 'text'
  | 'component'
  | 'fragment'
  | 'comment'

// 渲染节点数据
export interface NeRenderNode {
  id?: string | number
  type: NeRenderNodeType
  tag?: string
  text?: string
  component?: Component | string
  props?: Record<string, any>
  attrs?: Record<string, any>
  style?: Record<string, any>
  className?: string | string[]
  children?: NeRenderNode[]
  key?: string | number
  ref?: string
  slot?: string
  // 条件渲染
  if?: boolean | (() => boolean)
  show?: boolean | (() => boolean)
  // 循环渲染
  for?: {
    items: any[]
    itemKey?: string
    indexKey?: string
  }
  // 事件处理
  events?: Record<string, (...args: any[]) => void>
  // 自定义数据
  [key: string]: any
}

// 渲染配置
export interface NeRenderConfig {
  // 是否启用严格模式
  strict?: boolean
  // 自定义组件映射
  components?: Record<string, Component>
  // 自定义指令
  directives?: Record<string, any>
  // 全局属性
  globalProps?: Record<string, any>
  // 错误处理
  onError?: (error: Error, node: NeRenderNode) => void
  // 渲染前钩子
  beforeRender?: (node: NeRenderNode) => NeRenderNode | void
  // 渲染后钩子
  afterRender?: (vnode: VNode, node: NeRenderNode) => VNode | void
}

export const neRenderCoreProps = buildProps({
  // 渲染节点数据
  nodes: buildProp({
    type: [Object, Array] as () => NeRenderNode | NeRenderNode[],
    required: true,
  }),
  
  // 渲染配置
  config: buildProp({
    type: Object as () => NeRenderConfig,
    default: () => ({}),
  }),
  
  // 根元素标签
  tag: buildProp({
    type: String,
    default: 'div',
  }),
  
  // 是否启用严格模式
  strict: buildProp({
    type: Boolean,
    default: false,
  }),
  
  // 是否启用调试模式
  debug: buildProp({
    type: Boolean,
    default: false,
  }),
} as const)

export type NeRenderCoreProps = ExtractPropTypes<typeof neRenderCoreProps>

// 事件类型
export type NeRenderCoreEmits = {
  (e: 'render', node: NeRenderNode, vnode: VNode): void
  (e: 'error', error: Error, node: NeRenderNode): void
  (e: 'beforeRender', node: NeRenderNode): void
  (e: 'afterRender', vnode: VNode, node: NeRenderNode): void
}
