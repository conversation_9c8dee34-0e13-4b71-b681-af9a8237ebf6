<template>
  <component :is="tag" :class="rootClass" :data-debug="debug">
    <template v-if="Array.isArray(nodes)">
      <RenderNode
        v-for="(node, index) in nodes"
        :key="node.key || node.id || index"
        :node="node"
        :config="mergedConfig"
        @render="handleRender"
        @error="handleError"
        @before-render="handleBeforeRender"
        @after-render="handleAfterRender"
      />
    </template>
    <template v-else>
      <RenderNode
        :node="nodes"
        :config="mergedConfig"
        @render="handleRender"
        @error="handleError"
        @before-render="handleBeforeRender"
        @after-render="handleAfterRender"
      />
    </template>
  </component>
</template>

<script lang="ts" setup>
import {
  Comment,
  Fragment,
  computed,
  defineComponent,
  h,
  resolveComponent,
} from 'vue'
import {
  type NeRenderConfig,
  type NeRenderCoreEmits,
  type NeRenderNode,
  neRenderCoreProps,
} from './render-core'

defineOptions({
  name: 'NeRenderCore',
})

const props = defineProps(neRenderCoreProps)
const emit = defineEmits<NeRenderCoreEmits>()

// 合并配置
const mergedConfig = computed<NeRenderConfig>(() => ({
  strict: props.strict,
  ...props.config,
}))

// 根元素类名
const rootClass = computed(() => [
  'ne-render-core',
  {
    'ne-render-core--strict': mergedConfig.value.strict,
    'ne-render-core--debug': props.debug,
  },
])

// 渲染节点组件
const RenderNode = defineComponent({
  name: 'RenderNode',
  props: {
    node: {
      type: Object as () => NeRenderNode,
      required: true,
    },
    config: {
      type: Object as () => NeRenderConfig,
      required: true,
    },
  },
  emits: ['render', 'error', 'beforeRender', 'afterRender'],
  setup(props, { emit }) {
    const renderNode = (node: NeRenderNode): any => {
      try {
        // 触发渲染前钩子
        emit('beforeRender', node)
        const processedNode = props.config.beforeRender?.(node) || node

        // 条件渲染检查
        if (processedNode.if !== undefined) {
          const condition =
            typeof processedNode.if === 'function'
              ? processedNode.if()
              : processedNode.if
          if (!condition) return null
        }

        if (processedNode.show !== undefined) {
          const show =
            typeof processedNode.show === 'function'
              ? processedNode.show()
              : processedNode.show
          if (!show) return h('div', { style: { display: 'none' } })
        }

        let vnode: any

        // 循环渲染
        if (processedNode.for) {
          const {
            items,
            itemKey = 'item',
            indexKey = 'index',
          } = processedNode.for
          return items.map((item: any, index: number) => {
            const childNode = {
              ...processedNode,
              for: undefined,
              props: {
                ...processedNode.props,
                [itemKey]: item,
                [indexKey]: index,
              },
            }
            return renderNode(childNode)
          })
        }

        // 根据类型渲染
        switch (processedNode.type) {
          case 'text':
            vnode = processedNode.text || ''
            break

          case 'comment':
            vnode = h(Comment, processedNode.text || '')
            break

          case 'fragment':
            vnode = h(
              Fragment,
              {},
              processedNode.children?.map((child) => renderNode(child))
            )
            break

          case 'component':
            if (processedNode.component) {
              const component =
                typeof processedNode.component === 'string'
                  ? props.config.components?.[processedNode.component] ||
                    resolveComponent(processedNode.component)
                  : processedNode.component

              vnode = h(
                component,
                {
                  ...props.config.globalProps,
                  ...processedNode.props,
                  ...processedNode.attrs,
                  class: processedNode.className,
                  style: processedNode.style,
                  ref: processedNode.ref,
                  ...processedNode.events,
                },
                processedNode.children?.map((child) => renderNode(child))
              )
            }
            break

          case 'element':
          default:
            const tag = processedNode.tag || 'div'
            const children = processedNode.children?.map((child) =>
              renderNode(child)
            )

            vnode = h(
              tag,
              {
                ...props.config.globalProps,
                ...processedNode.props,
                ...processedNode.attrs,
                class: processedNode.className,
                style: processedNode.style,
                ref: processedNode.ref,
                ...processedNode.events,
              },
              children
            )
            break
        }

        // 触发渲染后钩子
        const finalVnode =
          props.config.afterRender?.(vnode, processedNode) || vnode
        emit('afterRender', finalVnode, processedNode)
        emit('render', processedNode, finalVnode)

        return finalVnode
      } catch (error) {
        emit('error', error as Error, node)
        props.config.onError?.(error as Error, node)

        if (props.config.strict) {
          throw error
        }

        return h(
          'div',
          {
            class: 'ne-render-core__error',
            title: (error as Error).message,
          },
          'Render Error'
        )
      }
    }

    return () => renderNode(props.node)
  },
})

// 事件处理
const handleRender = (node: NeRenderNode, vnode: any) => {
  emit('render', node, vnode)
}

const handleError = (error: Error, node: NeRenderNode) => {
  emit('error', error, node)
}

const handleBeforeRender = (node: NeRenderNode) => {
  emit('beforeRender', node)
}

const handleAfterRender = (vnode: any, node: NeRenderNode) => {
  emit('afterRender', vnode, node)
}
</script>

<style scoped>
.ne-render-core {
  width: 100%;
}

.ne-render-core--debug {
  border: 1px dashed #ccc;
  position: relative;
}

.ne-render-core--debug::before {
  content: 'DEBUG MODE';
  position: absolute;
  top: -1px;
  right: -1px;
  background: #ff6b6b;
  color: white;
  font-size: 10px;
  padding: 2px 4px;
  border-radius: 0 0 0 4px;
}

.ne-render-core__error {
  padding: 8px 12px;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  color: #721c24;
  font-size: 14px;
  margin: 4px 0;
}

.ne-render-core__error:hover {
  background-color: #f1b0b7;
}
</style>
