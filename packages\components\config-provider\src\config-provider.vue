<template>
  <el-config-provider v-bind="props">
    <slot />
  </el-config-provider>
</template>

<script setup lang="ts">
import { ElConfigProvider, configProviderProps } from 'element-plus'
import { neConfigProviderProps } from './types'

// 定义组件选项
defineOptions({
  name: 'NeConfigProvider',
})

const props = defineProps({ ...configProviderProps, ...neConfigProviderProps })
</script>

<style scoped>
.ne-config-provider {
  width: 100%;
  height: 100%;
}
</style>
