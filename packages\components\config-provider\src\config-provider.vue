<template>
  <div
    :class="rootClass"
    :style="rootStyle"
    :data-theme="dark ? 'dark' : 'light'"
  >
    <slot />
  </div>
</template>

<script lang="ts" setup>
import { computed, provide, watchEffect } from 'vue'
import { useLocale } from '@neue-plus/hooks'
import { neConfigProviderProps, type NeConfigContext } from './config-provider'

defineOptions({
  name: 'NeConfigProvider',
})

const props = defineProps(neConfigProviderProps)

// 使用语言钩子
const { locale } = useLocale()

// 计算根元素类名
const rootClass = computed(() => {
  return [
    `${props.namespace}-config-provider`,
    {
      [`${props.namespace}-config-provider--dark`]: props.dark,
      [`${props.namespace}-config-provider--strict`]: props.strict,
    },
  ]
})

// 计算根元素样式
const rootStyle = computed(() => {
  const styles: Record<string, any> = {}
  
  // 应用主题配置
  if (props.theme.primaryColor) {
    styles['--ne-color-primary'] = props.theme.primaryColor
  }
  if (props.theme.successColor) {
    styles['--ne-color-success'] = props.theme.successColor
  }
  if (props.theme.warningColor) {
    styles['--ne-color-warning'] = props.theme.warningColor
  }
  if (props.theme.dangerColor) {
    styles['--ne-color-danger'] = props.theme.dangerColor
  }
  if (props.theme.infoColor) {
    styles['--ne-color-info'] = props.theme.infoColor
  }
  if (props.theme.borderRadius) {
    styles['--ne-border-radius'] = props.theme.borderRadius
  }
  if (props.theme.fontSize) {
    styles['--ne-font-size'] = props.theme.fontSize
  }
  if (props.theme.fontFamily) {
    styles['--ne-font-family'] = props.theme.fontFamily
  }
  
  // z-index 基础值
  styles['--ne-z-index-base'] = props.zIndex
  
  return styles
})

// 创建配置上下文
const configContext = computed<NeConfigContext>(() => ({
  locale: props.locale || locale.value,
  size: props.size,
  theme: props.theme,
  dark: props.dark,
  disabled: props.disabled,
  zIndex: props.zIndex,
  namespace: props.namespace,
  strict: props.strict,
}))

// 提供配置上下文
provide('neConfigContext', configContext)

// 监听暗黑模式变化，更新 HTML 类名
watchEffect(() => {
  if (typeof document !== 'undefined') {
    const htmlElement = document.documentElement
    if (props.dark) {
      htmlElement.classList.add('dark')
    } else {
      htmlElement.classList.remove('dark')
    }
  }
})
</script>

<style scoped>
.ne-config-provider {
  width: 100%;
  height: 100%;
}

.ne-config-provider--dark {
  color-scheme: dark;
}

.ne-config-provider--strict {
  /* 严格模式下的样式 */
}
</style>
