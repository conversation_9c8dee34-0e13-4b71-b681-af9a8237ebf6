import { resolveComponent } from 'vue'

// 组件映射表
const componentMap: Record<string, any> = {
  // Element Plus 组件
  'el-button': () => import('element-plus').then(m => m.ElButton),
  'el-input': () => import('element-plus').then(m => m.ElInput),
  'el-select': () => import('element-plus').then(m => m.ElSelect),
  'el-option': () => import('element-plus').then(m => m.ElOption),
  'el-form': () => import('element-plus').then(m => m.ElForm),
  'el-form-item': () => import('element-plus').then(m => m.ElFormItem),
  'el-card': () => import('element-plus').then(m => m.ElCard),
  'el-table': () => import('element-plus').then(m => m.ElTable),
  'el-table-column': () => import('element-plus').then(m => m.ElTableColumn),
  'el-pagination': () => import('element-plus').then(m => m.ElPagination),
  'el-dialog': () => import('element-plus').then(m => m.ElDialog),
  'el-drawer': () => import('element-plus').then(m => m.ElDrawer),
  'el-tabs': () => import('element-plus').then(m => m.ElTabs),
  'el-tab-pane': () => import('element-plus').then(m => m.ElTabPane),
  'el-collapse': () => import('element-plus').then(m => m.ElCollapse),
  'el-collapse-item': () => import('element-plus').then(m => m.ElCollapseItem),
  'el-menu': () => import('element-plus').then(m => m.ElMenu),
  'el-menu-item': () => import('element-plus').then(m => m.ElMenuItem),
  'el-submenu': () => import('element-plus').then(m => m.ElSubmenu),
  'el-breadcrumb': () => import('element-plus').then(m => m.ElBreadcrumb),
  'el-breadcrumb-item': () => import('element-plus').then(m => m.ElBreadcrumbItem),
  'el-steps': () => import('element-plus').then(m => m.ElSteps),
  'el-step': () => import('element-plus').then(m => m.ElStep),
  'el-radio': () => import('element-plus').then(m => m.ElRadio),
  'el-radio-group': () => import('element-plus').then(m => m.ElRadioGroup),
  'el-checkbox': () => import('element-plus').then(m => m.ElCheckbox),
  'el-checkbox-group': () => import('element-plus').then(m => m.ElCheckboxGroup),
  'el-switch': () => import('element-plus').then(m => m.ElSwitch),
  'el-slider': () => import('element-plus').then(m => m.ElSlider),
  'el-time-picker': () => import('element-plus').then(m => m.ElTimePicker),
  'el-date-picker': () => import('element-plus').then(m => m.ElDatePicker),
  'el-upload': () => import('element-plus').then(m => m.ElUpload),
  'el-rate': () => import('element-plus').then(m => m.ElRate),
  'el-color-picker': () => import('element-plus').then(m => m.ElColorPicker),
  'el-transfer': () => import('element-plus').then(m => m.ElTransfer),
  'el-tree': () => import('element-plus').then(m => m.ElTree),
  'el-cascader': () => import('element-plus').then(m => m.ElCascader),
  'el-autocomplete': () => import('element-plus').then(m => m.ElAutocomplete),
  'el-tooltip': () => import('element-plus').then(m => m.ElTooltip),
  'el-popover': () => import('element-plus').then(m => m.ElPopover),
  'el-popconfirm': () => import('element-plus').then(m => m.ElPopconfirm),
  'el-alert': () => import('element-plus').then(m => m.ElAlert),
  'el-loading': () => import('element-plus').then(m => m.ElLoading),
  'el-message': () => import('element-plus').then(m => m.ElMessage),
  'el-notification': () => import('element-plus').then(m => m.ElNotification),
  'el-progress': () => import('element-plus').then(m => m.ElProgress),
  'el-skeleton': () => import('element-plus').then(m => m.ElSkeleton),
  'el-skeleton-item': () => import('element-plus').then(m => m.ElSkeletonItem),
  'el-empty': () => import('element-plus').then(m => m.ElEmpty),
  'el-result': () => import('element-plus').then(m => m.ElResult),
  'el-image': () => import('element-plus').then(m => m.ElImage),
  'el-avatar': () => import('element-plus').then(m => m.ElAvatar),
  'el-badge': () => import('element-plus').then(m => m.ElBadge),
  'el-tag': () => import('element-plus').then(m => m.ElTag),
  'el-divider': () => import('element-plus').then(m => m.ElDivider),
  'el-space': () => import('element-plus').then(m => m.ElSpace),
  'el-row': () => import('element-plus').then(m => m.ElRow),
  'el-col': () => import('element-plus').then(m => m.ElCol),
  'el-container': () => import('element-plus').then(m => m.ElContainer),
  'el-header': () => import('element-plus').then(m => m.ElHeader),
  'el-aside': () => import('element-plus').then(m => m.ElAside),
  'el-main': () => import('element-plus').then(m => m.ElMain),
  'el-footer': () => import('element-plus').then(m => m.ElFooter),
  
  // Neue Plus 组件
  'ne-card': () => import('../../card').then(m => m.NeCard),
  'ne-table': () => import('../../table').then(m => m.NeTable),
  'ne-pro-table': () => import('../../pro-table').then(m => m.NeProTable),
  'ne-dynamic-form': () => import('../../dynamic-form').then(m => m.NeDynamicForm),
  'ne-radio-group': () => import('../../radio-group').then(m => m.NeRadioGroup),
  'ne-config-provider': () => import('../../config-provider').then(m => m.NeConfigProvider),
}

/**
 * 根据组件名称获取组件
 * @param componentName 组件名称
 * @returns 组件构造函数或组件名称
 */
export function getComponentByName(componentName: string) {
  try {
    // 首先尝试从组件映射表中获取
    if (componentMap[componentName]) {
      return componentMap[componentName]
    }
    
    // 尝试使用 Vue 的 resolveComponent
    return resolveComponent(componentName)
  } catch (error) {
    console.warn(`Component "${componentName}" not found, using as tag name`)
    return componentName
  }
}

/**
 * 注册自定义组件
 * @param name 组件名称
 * @param component 组件构造函数
 */
export function registerComponent(name: string, component: any) {
  componentMap[name] = component
}

/**
 * 批量注册组件
 * @param components 组件映射对象
 */
export function registerComponents(components: Record<string, any>) {
  Object.assign(componentMap, components)
}

/**
 * 获取所有已注册的组件名称
 * @returns 组件名称数组
 */
export function getRegisteredComponentNames(): string[] {
  return Object.keys(componentMap)
}

/**
 * 检查组件是否已注册
 * @param name 组件名称
 * @returns 是否已注册
 */
export function isComponentRegistered(name: string): boolean {
  return name in componentMap
}
