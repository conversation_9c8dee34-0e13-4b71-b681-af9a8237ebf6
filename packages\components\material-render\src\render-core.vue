import { defineComponent, defineAsyncComponent, computed, h, inject } from "vue";
import { getComponentByName } from "../../utils";
import { NeMaterialElementProps } from "./types";
import { getComponentEvent, useElementProps } from "../../hooks";
import { SchemaRefs } from "@/hooks/useSchemaRefs";

const AsyncNeMaterialRender = defineAsyncComponent(() => import(".")); // ✅ 延迟加载防止循环引用

function resolveSlots(config: NeMaterialElementProps) {
  const slots: Record<string, () => any> = {};

  if (config.elements?.length) {
    slots.default = () =>
      config.elements!.map((child, index) =>
        h(AsyncNeMaterialRender, { key: index, config: child })
      );
  }

  if (config.slots) {
    console.log(config.slots, "slots");
    for (const [slotName, content] of Object.entries(config.slots)) {
      if (Array.isArray(content)) {
        slots[slotName] = () =>
          h(
            "div",
            content.map((child, index) => h(AsyncNeMaterialRender, { key: index, config: child }))
          );
      } else {
        console.log(content, "content");
        slots[slotName] = () => h("span", content);
      }
    }
  }
  console.log(slots, "slots");
  return slots;
}
const NeRenderCore = defineComponent({
  name: "NeRenderCore",
  props: {
    config: {
      type: Object as () => NeMaterialElementProps,
      required: true,
    },
  },
  setup(props) {
    const { config } = props;
    const resolvedComponent = computed(() => getComponentByName(config.type));
    const { elPprops } = useElementProps(config);
    const refs: any = inject("refs");
    const eventHandlers = getComponentEvent(config, refs);
    return () => {
        return h(
        resolvedComponent.value||config.type,
          {
            ref: (el: any) => {
              if (refs && config.id) {
                refs[config.id] = el;
              }
            },
            ...elPprops,
            ...eventHandlers,
          },
          resolveSlots(config)
        );
    };
  },
});
export default NeRenderCore;