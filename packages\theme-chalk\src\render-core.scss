@use 'mixins/mixins' as *;
@use 'mixins/var' as *;
@use 'common/var' as *;

@include b(render-core) {
  width: 100%;

  @include m(strict) {
    // 严格模式样式
  }

  @include m(debug) {
    border: 1px dashed getCssVar('color', 'warning');
    position: relative;

    &::before {
      content: 'DEBUG MODE';
      position: absolute;
      top: -1px;
      right: -1px;
      background: getCssVar('color', 'danger');
      color: getCssVar('color', 'white');
      font-size: getCssVar('font-size', 'extra-small');
      padding: getCssVar('render-core', 'debug-badge-padding');
      border-radius: 0 0 0 getCssVar('border-radius', 'small');
      z-index: 1000;
      font-weight: 600;
      line-height: 1;
    }
  }

  @include e(error) {
    padding: getCssVar('render-core', 'error-padding');
    background-color: getCssVar('color', 'danger', 'light-9');
    border: 1px solid getCssVar('color', 'danger', 'light-5');
    border-radius: getCssVar('border-radius', 'base');
    color: getCssVar('color', 'danger', 'dark-2');
    font-size: getCssVar('font-size', 'base');
    margin: getCssVar('render-core', 'error-margin');
    cursor: help;
    transition: background-color getCssVar('transition-duration');

    &:hover {
      background-color: getCssVar('color', 'danger', 'light-8');
    }

    &::before {
      content: '⚠️ ';
      margin-right: 4px;
    }
  }

  @include e(loading) {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: getCssVar('render-core', 'loading-padding');
    color: getCssVar('text-color', 'secondary');
    font-size: getCssVar('font-size', 'base');

    &::before {
      content: '';
      width: 16px;
      height: 16px;
      border: 2px solid getCssVar('color', 'primary', 'light-5');
      border-top-color: getCssVar('color', 'primary');
      border-radius: 50%;
      margin-right: 8px;
      animation: ne-render-core-spin 1s linear infinite;
    }
  }

  @include e(empty) {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: getCssVar('render-core', 'empty-padding');
    color: getCssVar('text-color', 'placeholder');
    font-size: getCssVar('font-size', 'base');

    &::before {
      content: '📄 ';
      margin-right: 4px;
      opacity: 0.5;
    }
  }
}

@keyframes ne-render-core-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
